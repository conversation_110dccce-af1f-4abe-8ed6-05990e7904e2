/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 */
import { createElement, useState } from 'rax';
import View from 'rax-view';
import TabHeader from '@ali/rxpi-tab-header';

import UploadFile from './components/upload-file';
import UploadImage from './components/upload-image';
import './index.less';

export default function FliggyImageUploadDemo() {
  const [activeIndex, setActiveIndex] = useState(0);
  const onSelect = ({ selectedIndex }) => {
    setActiveIndex(selectedIndex);
  };

  return (
    <View className="container">
      <TabHeader
        dataSource={['图片上传', '文件上传']}
        type="normal-noborder"
        tabSelectedTextColor="#000"
        tabSelectedBgColor="#FFDB00"
        onSelect={onSelect}
        defaultIndex={activeIndex}
      />
      {activeIndex === 0 && <UploadImage />}
      {activeIndex === 1 && <UploadFile />}
    </View>
  );
}