<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <title>TestImageUpload - Demo</title>
  <meta name="aplus-terminal" content="1" />
  <meta name="weex-viewport" content="750" />
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover">
  <!-- <meta name="data-spm" content="181.SPM"/> -->
  <meta name="page-name" content="TestImageUpload_demo" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black" />
  <meta name="apple-touch-fullscreen" content="yes" />
  <meta name="format-detection" content="telephone=no, email=no" />
  <link rel="dns-prefetch" href="//g.alicdn.com">
  <link rel="dns-prefetch" href="//gw.alicdn.com">
  <link rel="dns-prefetch" href="//log.mmstat.com">
  <link rel="dns-prefetch" href="//api.m.taobao.com">
  <link rel="dns-prefetch" href="//api.m.alitrip.com">
  <style>
    html,
    body {
      -ms-overflow-style: scrollbar;
      -webkit-tap-highlight-color: transparent;
      padding: 0;
      margin: 0;
      width: 100%;
      height: 100%;
    }

    body {
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: 0;
      font-family: BlinkMacSystemFont, 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    }

    input[type="search"]::-webkit-search-decoration,
    input[type="search"]::-webkit-search-cancel-button {
      -webkit-appearance: none !important;
    }

    body [web-sticky] {
      position: -webkit-sticky !important;
      position: sticky !important;
    }
  </style>
  <script>
    window.__bl = {
      config: {
        pid: "haa3j9plpi@e7665cc0d14fd3b",
        appType: "web",
        imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
        sendResource: true,
        enableLinkTrace: true,
        behavior: true
      }
    }
  </script>
  <script type="text/javascript" src="https://sdk.rum.aliyuncs.com/v1/bl.js" crossorigin></script>
  <!-- 调试工具 VConsole -->
  <script src="https://unpkg.com/vconsole/dist/vconsole.min.js"></script>
  <script>
    // 初始化 VConsole
    var vConsole = new VConsole();
    console.log('Hello world');
  </script>

  <!-- 调试工具 eruda
  <script src="//cdn.jsdelivr.net/npm/eruda"></script>
  <script>eruda.init();</script> -->
</head>

<body data-noaplus="true">
  <script src="https://g.alicdn.com/mpi/base/0.5.1/seed-weex-wv-min.js"></script>
  <script src="./index.web.js"></script>
</body>

</html>