import { SubDomain } from "src/interface";

export const defaultUploadConfig = {
  mode: undefined, // 缺省，默认为'ORIGIN',
  prefix: 'temp', // 实际无默认值；强烈建议开发测试时加入此prefix=temp，避免开发过程中测试的图片留存太久了造成oss空间浪费，此前缀文件资源只留存三天。
  // 默认不传，这里测试用wapa
  subDomain: 'wapa' as SubDomain, // 'm' | 'wapa' | 'waptest', 不传，自动匹配环境，传入则固定使用传入环境。预发环境不稳定时，可以通过 subDomain传入m 在预发环境改用线上环境
  expires: undefined, // 不传，图片将作为公共图片上传，返回fli.alicdn.com 图片地址，不需要加签即可访问；传入有效值，图片作为隐私图片上传，返回 images1.fliggy.com 图片地址，地址有效期限为传入值，单位（秒）；时间超出有效值需要重新加签才能正常访问
  process: undefined, // 图像处理指令,比如加水印
  // process:
  //   "image/watermark,image_dXRpbHMvaWRDYXJkV2F0ZXJNYXJrZXIucG5nP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLFBfODA=,t_90,g_center,voffset_0",
  customBucket: undefined, // 定制 bucket 上传（普通接入无需传入）必须上传base64文件
  onBeforeRequest: undefined,
  onAfterRequest: undefined,
};

export const defaultConfig = {
  camera: false,
  album: false,
  enablePDF: false,
  maxSelect: 1,
  chooseByNative: false,
  openVerify: false,
  skipImageCompress: undefined, // 无默认值； 缺省或 false 不跳过压缩
  autoCompress: true,
  // imgCanvasOptions: {},
  quality: undefined, // 不传则采用默认压缩算法
  onStatusChange: undefined,
  uploadConfig: defaultUploadConfig,
};