import { createElement, useCallback } from 'rax';
import View from 'rax-view';
import './index.css';

import { UploadConfigProps } from '../../../../index';

interface Props {
  onUploadConfigChange: (config: UploadConfigProps) => void;
  config: UploadConfigProps;
  showDesc?: boolean;
}

// uploadConfig: {
//   mode: undefined, // 缺省，默认为'ORIGIN',
//   prefix: 'temp', // 实际无默认值；强烈建议开发测试时加入此prefix=temp，避免开发过程中测试的图片留存太久了造成oss空间浪费，此前缀文件资源只留存三天。
//   subDomain: undefined, // 'm' | 'wapa' | 'waptest', 不传，自动匹配环境，传入则固定使用传入环境。预发环境不稳定时，可以通过 subDomain传入m 在预发环境改用线上环境
//   expires: undefined, // 不传，图片将作为公共图片上传，返回fli.alicdn.com 图片地址，不需要加签即可访问；传入有效值，图片作为隐私图片上传，返回 images1.fliggy.com 图片地址，地址有效期限为传入值，单位（秒）；时间超出有效值需要重新加签才能正常访问
//   process: undefined, // 图像处理指令,比如加水印
//   customBucket: undefined, // 定制 bucket 上传（普通接入无需传入）必须上传base64文件
//   onBeforeRequest: undefined,
//   onAfterRequest: undefined,
// },

const UploadConfig = ({ onUploadConfigChange, config, showDesc }: Props) => {
  // const [config, setUploadConfig] = useState<ImageUploadProps>(defaultUploadConfig);

  const handleChange = useCallback(
    (e) => {
      const { name, value } = e.target;
      let changeOption = {};
      switch (name) {
        case 'mode':
        case 'subDomain':
          if (value === 'undefined') {
            changeOption = {
              [name]: undefined,
            };
          } else {
            changeOption = {
              [name]: value,
            };
          }
          break;
        case 'customBucket':
          if (value === 'undefined') {
            changeOption = {
              [name]: undefined,
            };
          } else {
            changeOption = {
              [name]: value,
            };
          }
          break;
        case 'expires':
          if (value === '') {
            changeOption = {
              expires: undefined,
            };
          } else {
            changeOption = {
              expires: Number(value),
            };
          }
          break;
        case 'prefix':
          changeOption = {
            prefix: value,
          };
          break;
        default:
          changeOption = {
            [name]: value,
          };
      }

      onUploadConfigChange({
        ...config,
        ...changeOption,
      });
    },
    [config, onUploadConfigChange],
  );

  return (
    <View>
      <View className="title">图片上传</View>
      <View className="input-wrap">
        prefix:
        <input
          type="text"
          name="prefix"
          value={config.prefix}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        文件前缀,
        可以找悦加（396390）申请获取；建议开发测试时加入此prefix=temp，避免开发过程中测试的图片留存太久了造成oss空间浪费，此前缀文件资源只留存三天。
      </View>
      <View className="input-wrap" style={{ flexDirection: 'row' }}>
        mode:
        <select
          name="mode"
          id="mode"
          value={config.mode}
          onChange={handleChange}
        >
          <option value="undefined" selected={config.mode === undefined}>
            不选择
          </option>
          <option value="ORIGIN" selected={config.mode === 'ORIGIN'}>
            ORIGIN(原通道)
          </option>
          <option value="OSS_URL" selected={config.mode === 'OSS_URL'}>
            OSS_URL
          </option>
          <option value="OSS_TOKEN" selected={config.mode === 'OSS_TOKEN'}>
            OSS_TOKEN
          </option>
        </select>
      </View>
      <View className="desc" x-if={showDesc}>
        'ORIGIN': 3.0.0版本前的原有上传模式，faas 转发上传 oss 服务
        <br />
        'OSS_URL': oss 直传，put 请求上传，不依赖 oss sdk
        <br />
        'OSS_TOKEN': oss 直传， 依赖 oss sdk
      </View>
      <View className="input-wrap" style={{ flexDirection: 'row' }}>
        subDomain:
        <select
          name="subDomain"
          id="subDomain"
          value={config.subDomain}
          onChange={handleChange}
        >
          <option value="undefined" selected={config.subDomain === undefined}>
            不选择
          </option>
          <option value="m" selected={config.subDomain === 'm'}>
            正式环境 m
          </option>
          <option value="wapa" selected={config.subDomain === 'wapa'}>
            预发环境 wapa
          </option>
          <option
            disabled
            value="waptest"
            selected={config.subDomain === 'waptest'}
          >
            waptest
          </option>
        </select>
      </View>
      <View className="desc" x-if={showDesc}>
        mtop请求二级域，当前环境（日常 'waptest'、预发 'wapa'、生产
        'm'；不传，自动匹配环境，传入则固定使用传入环境。预发环境不稳定时，可以通过
        subDomain传入m
        在预发环境改用线上环境。（仅web端有效，飞猪淘宝支付宝切换环境通过 app
        内工具切换， eg.Atom）
      </View>
      <View className="input-wrap" style={{ flexDirection: 'row' }}>
        expires(单位:秒,缺省为公共图片):
        <input
          type="number"
          name="expires"
          min={0}
          step={1}
          value={config.expires}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        1.公共图片上传：不传expires，返回fli.alicdn.com
        图片地址，不需要加签即可访问 <br />
        2.隐私图片上传：传入expires有效值，图片作为隐私图片上传，返回
        images1.fliggy.com
        图片地址，地址有效期限为传入值，单位（秒）；时间超出有效值需要重新加签才能正常访问。
        <br />
      </View>
      <View className="input-wrap" style={{ flexDirection: 'row' }}>
        customBucket:
        <select
          name="customBucket"
          id="customBucket"
          value={config.customBucket}
          onChange={handleChange}
        >
          <option value="undefined" selected={config.customBucket === undefined}>
            不选择
          </option>
          <option
            value="fliggy-insurance-claim"
            selected={config.customBucket === 'fliggy-insurance-claim'}
          >
            fliggy-insurance-claim
          </option>
          <option
            value="tbvideo-alitrip"
            selected={config.customBucket === 'tbvideo-alitrip'}
          >
            tbvideo-alitrip
          </option>
        </select>
      </View>
      <View className="desc" x-if={showDesc}>
        自定义bucket上传(定制bucket上传为历史功能，只修bug，不再更新，也不再接收新的定制bucket需求)
      </View>
    </View>
  );
};

export default UploadConfig;
