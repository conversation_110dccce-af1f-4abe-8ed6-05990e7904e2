import { createElement, useCallback, useState } from 'rax';
import View from 'rax-view';
import './index.css';

import { Image2CanvasConfig } from '../../../../index';

interface Props {
  onConfigChange?: (config: Image2CanvasConfig) => void;
  config: Image2CanvasConfig;
}

const ImageCanvasOptions = ({ onConfigChange, config }: Props) => {
  const handleChange = useCallback(
    (e) => {
      const { name, value } = e.target;
      onConfigChange &&
        onConfigChange({
          ...config,
          [name]: value === '' ? undefined : Number(value),
        });
    },
    [config, onConfigChange],
  );

  const [wrap, setWrap] = useState(true);

  return (
    <View>
      <View
        className="image-option-title"
        onClick={() => {
          setWrap(!wrap);
        }}
      >
        图片转canvas设置(imgCanvasOptions)(存在问题，不必测试,后续可能移除)&gt;
      </View>
      <View x-if={!wrap}>
        <View className="input-wrap">
          width:
          <input
            type="number"
            name="width"
            min={0}
            step={10}
            id="width"
            value={config.width}
            onChange={handleChange}
          />
        </View>
        <View className="input-wrap">
          height:
          <input
            type="number"
            name="height"
            min={0}
            step={10}
            id="height"
            value={config.height}
            onChange={handleChange}
          />
        </View>

        <View className="input-wrap">
          scale:
          <input
            type="number"
            name="scale"
            max={10}
            min={0}
            id="scale"
            value={config.scale}
            onChange={handleChange}
          />
        </View>

        <View className="input-wrap">
          orientation:
          <select
            name="orientation"
            id="orientation"
            value={config.orientation}
            onChange={handleChange}
          >
            <option value="" selected={config.mode === 'orientation'}>
              不选
            </option>
            <option value="1" selected={config.mode === 'orientation'}>
              1: 不变
            </option>
            <option value="2" selected={config.mode === 'orientation'}>
              2: 水平翻转
            </option>
            <option value="3" selected={config.mode === 'orientation'}>
              3: 180度
            </option>
            <option value="4" selected={config.mode === 'orientation'}>
              4:垂直翻转
            </option>
            <option value="5" selected={config.mode === 'orientation'}>
              5:顺时针 90°+水平翻转
            </option>
            <option value="6" selected={config.mode === 'orientation'}>
              6:顺时针 90°
            </option>
            <option value="7" selected={config.mode === 'orientation'}>
              7:顺时针 90°+垂直翻转
            </option>
            <option value="8" selected={config.mode === 'orientation'}>
              8:逆时针 90°
            </option>
          </select>
        </View>
      </View>
    </View>
  );
};

export default ImageCanvasOptions;
