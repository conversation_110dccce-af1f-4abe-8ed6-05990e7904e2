import { createElement, useCallback } from 'rax';
import View from 'rax-view';
import UploadConfig from './UploadConfig';
// import ImageCanvasOptions from './ImageCanvasOptions';
import {
  // Image2CanvasConfig,
  ImageUploadProps,
  UploadConfigProps,
} from '../../../../index';
import './index.css';

interface ConfigProps {
  onConfigChange: (config: ImageUploadProps) => void;
  config: ImageUploadProps;
  showDesc?: boolean; // 是否显示配置说明信息
}

// export const defaultConfig = {
//   camera: false,
//   album: false,
//   enablePDF: false,
//   maxSelect: 1,
//   chooseByNative: false,
//   openVerify: false,
//   skipImageCompress: undefined, // 无默认值； 缺省或 false 不跳过压缩
//   autoCompress: true,
//   imgCanvasOptions: {},
//   quality: undefined, // 不传则采用默认压缩算法
//   onStatusChange: (...args) => {
//     console.log('onStatusChange args:', args);
//   },
//   uploadConfig: {
//     mode: undefined, // 缺省，默认为'ORIGIN',
//     prefix: 'temp', // 实际无默认值；强烈建议开发测试时加入此prefix=temp，避免开发过程中测试的图片留存太久了造成oss空间浪费，此前缀文件资源只留存三天。
//     subDomain: undefined, // 'm' | 'wapa' | 'waptest', 不传，自动匹配环境，传入则固定使用传入环境。预发环境不稳定时，可以通过 subDomain传入m 在预发环境改用线上环境
//     expires: undefined, // 不传，图片将作为公共图片上传，返回fli.alicdn.com 图片地址，不需要加签即可访问；传入有效值，图片作为隐私图片上传，返回 images1.fliggy.com 图片地址，地址有效期限为传入值，单位（秒）；时间超出有效值需要重新加签才能正常访问
//     process: undefined, // 图像处理指令,比如加水印
//     // process:
//     //   "image/watermark,image_dXRpbHMvaWRDYXJkV2F0ZXJNYXJrZXIucG5nP3gtb3NzLXByb2Nlc3M9aW1hZ2UvcmVzaXplLFBfODA=,t_90,g_center,voffset_0",
//     customBucket: undefined, // 定制 bucket 上传（普通接入无需传入）必须上传base64文件
//     onBeforeRequest: undefined,
//     onAfterRequest: undefined,
//   },
// };

const Config = ({ onConfigChange, config, showDesc }: ConfigProps) => {
  const handleChange = useCallback(
    (e) => {
      const { name, value, type, checked } = e.target;
      let changeOption = {};
      if (type === 'checkbox') {
        changeOption = {
          [name]: checked,
        };
      } else {
        switch (name) {
          case 'maxSelect':
          case 'quality':
            if (value === '') {
              changeOption = {
                [name]: undefined,
              };
            } else {
              changeOption = {
                [name]: Number(value),
              };
            }
            break;
          default:
            changeOption = {
              [name]: value,
            };
        }
      }

      onConfigChange({
        ...config,
        ...changeOption,
      });
    },
    [config, onConfigChange],
  );

  const handleUploadConfigChange = useCallback(
    (uploadConfig: UploadConfigProps) => {
      onConfigChange({
        ...config,
        uploadConfig,
      });
    },
    [config, onConfigChange],
  );
  // const handleImageCanvasOptionsChange = useCallback(
  //   (imgCanvasOptions: Image2CanvasConfig) => {
  //     onConfigChange({
  //       ...config,
  //       imgCanvasOptions,
  //     });
  //   },
  //   [config, onConfigChange],
  // );

  return (
    <View className="config-wrap">
      <View className="title">图片获取</View>
      <View className="input-wrap">
        camera:
        <input
          type="checkbox"
          name="camera"
          checked={config.camera}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        是否走拍照模式，默认是false，如果走拍照，会忽视掉maxSelect及album参数。在IOS支付宝及Android端淘宝、支付宝
        且 chooseByNative 为false时，album 同时为true, 走相册，album 优先级高;
        其他情况，camera 优先级高。
      </View>
      <View className="input-wrap">
        album:
        <input
          type="checkbox"
          name="album"
          checked={config.album}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        是否走相册模式，默认是false; 在IOS支付宝及Android端淘宝支付宝 且
        chooseByNative 为false时，album 同时为true, 走相册，album 优先级高;
        其他情况，如果 camera 同时为true, 走拍照， camera 优先级高。
      </View>
      <View className="input-wrap">
        enablePDF:
        <input
          type="checkbox"
          name="enablePDF"
          checked={config.enablePDF}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        是否支持上传PDF， 为 true 时，会自动降级为h5 input获取文件
      </View>
      <View className="input-wrap">
        taobaoBizName:
        <input
          type="text"
          name="taobaoBizName"
          value={config.taobaoBizName}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        在淘宝app中使用uni-api获取图片需传入的bizName,
        需要上游业务单独申请并传入，不传将降级为h5-input元素获取
      </View>
      <View className="input-wrap">
        maxSelect:
        <input
          type="number"
          name="maxSelect"
          value={config.maxSelect}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        最多选择图片数，等于 1 的时候为单选，大于 1 为多选, 原生 h5
        只支持设置单选多选；android 淘宝端使用桥方法相册只支持单选
      </View>
      <View className="input-wrap">
        chooseByNative:
        <input
          type="checkbox"
          name="chooseByNative"
          checked={config.chooseByNative}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        是否使用h5 input获取文件；Android淘猪支+
        IOS猪支默认使用桥获取图片，可通过设置chooseByNative：true 降级使用 h5
        input方法获取图片{' '}
      </View>
      <View className="input-wrap">
        openVerify:
        <input
          type="checkbox"
          name="openVerify"
          checked={config.openVerify}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        是否开启校验功能，目前支持校验最大图片选择数量，超出了报错
        LIMIT_EXCEEDED
      </View>
      <br />
      <View className="title">图片处理</View>
      <View className="input-wrap">
        跳过压缩图像:
        <input
          type="checkbox"
          name="skipImageCompress"
          checked={config.skipImageCompress}
          onChange={handleChange}
        />
      </View>
      <View className="desc" x-if={showDesc}>
        是否跳过图片压缩环节，如果跳过，压缩相关props: `autoCompress`,
        压缩过程的`quality` 将无效; 缺省或 false 不跳过压缩。pdf会自动跳过压缩，
        skipImageCompress 不为true, 也不会进行压缩。
      </View>
      {config.skipImageCompress !== true && (
        <>
          <View className="input-wrap">
            是否自动压缩:
            <input
              type="checkbox"
              name="autoCompress"
              checked={config.autoCompress}
              onChange={handleChange}
            />
          </View>
          <View className="desc" x-if={showDesc}>
            是否自动压缩，将超过600k的图片压缩到600k以下； 不压缩pdf；
            如果传入有效的quality, 则不会进行自动压缩，quality 优先级高；
          </View>
          <View className="input-wrap">
            图片压缩质量(不传采用默认压缩算法):
            <input
              type="number"
              name="quality"
              step={0.1}
              min={0}
              max={1}
              value={config.quality}
              onChange={handleChange}
            />
          </View>
          <View className="desc" x-if={showDesc}>
            图片压缩质量，不传则采用默认压缩算法。介于0和1之间的数字，其中1是最高质量（最少压缩）而0是最低质量（最大压缩）
            作为第二个参数 `encoderOptions` 传入 `canvas.toDataURL('image/jpeg',
            encoderOptions)`，用以表示图像质量。
          </View>
          {/* <View
            className="input-wrap"
            style={{
              flexDirection: 'column',
              alignItems: 'flex-start',
            }}
          >
            <ImageCanvasOptions
              config={config.imgCanvasOptions}
              onConfigChange={handleImageCanvasOptionsChange}
            />
          </View> */}
        </>
      )}
      <br />
      <UploadConfig
        config={config.uploadConfig}
        onUploadConfigChange={handleUploadConfigChange}
        showDesc={showDesc}
      />
      <br />
      <View>
        <View className="title">当前配置</View>
        <pre style={{ fontSize: '10px', paddingLeft: '10px' }}>
          {JSON.stringify(config, null, 2)}
        </pre>
      </View>
    </View>
  );
};

export default Config;
