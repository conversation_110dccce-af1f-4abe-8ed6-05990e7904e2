@charset "UTF-8";

.config-wrap {
  font-size: 28rpx;
  padding-bottom: 10rpx;
  background:  rgba(255, 224, 51, 0.1);
  width: 100%;
}

.title {
  background-color: rgba(255, 224, 51, 0.5);
  width: 100%;
  padding: 5rpx 0;
  padding-left: 20rpx;
}
.desc {
  font-size: 20rpx;
  color: #999;
  padding-left: 24rpx;
}

.input-wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  margin-top: 16rpx;
  padding-left: 24rpx;
}

input {
  display: inline-block;
  width: 100rpx;
}

input[type='number'] {
  width: 80rpx;
}
