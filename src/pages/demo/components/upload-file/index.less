@charset "UTF-8";

.container {
  margin-top: 20rpx;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container-button:active {
  background-color: #f2f2f2;
}
.container-button {
  width: 400rpx;
  height: 78rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(255, 224, 51);
  border-radius: 50rpx;
  margin: 20rpx 0;
  font-size: 28rpx;
}
.url-text {
  font-size: 20rpx;
  max-width: 100vw;
  word-break: break-all;
}
.img, .iframe {
  max-width: 600rpx;
  max-height: 400rpx;
  object-fit: contain;
}

.h2 {
  padding: 2%;
  background: #ccc;
}
.component-container {
  padding: 2%;
  min-height: 750rpx;
  border: 1px solid #ccc;
}
