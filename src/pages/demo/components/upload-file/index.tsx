import { createElement, useEffect, useState } from 'rax';
import View from 'rax-view';

import './index.less';

import upload from '../../../../video-upload/index';

const defaultConfig = {
  maxSelect: 3,
  onStatusChange: (type, list) => {
    console.log('******', type, list);
  },
  uploadConfig: {
    // prefix: '2077-07-07_[id]',
    prefix: 'temp',
    subDomain: 'wapa',
    // expires: '300',
    // customBucket: "tbvideo-alitrip",
    // customBucket: 'fliggy-insurance',
    // process:
  },
};

export default function UploadFile() {
  const [urlList, setUrlList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [time, setTime] = useState(0);

  useEffect(() => {
    // @ts-ignore
    window.fileUploadConfig = defaultConfig;
  }, []);

  async function uploadFile() {
    if (loading) {
      return;
    }
    try {
      setLoading(true);
      const config = {
        ...defaultConfig,
        // @ts-ignore
        ...(window?.fileUploadConfig || {}),
        inputAccept: '*/*' // 允许上传所有类型文件
      };
      console.time(`uploadFiles${config.uploadConfig?.mode}`);
      const startTime = Date.now();
      const result = await upload(config);
      console.timeEnd(`uploadFiles${config.uploadConfig?.mode}`);
      const endTime = Date.now();
      setTime(endTime - startTime);
      setUrlList(result);
      console.log('*****result', result);
      setLoading(false);
    } catch (err) {
      console.log('***result_error', err);
      setLoading(false);
    }
  }

  return (
    <View className="container">
      <View className="container-button" onClick={uploadFile}>
        {loading ? '上传中' : '文件上传(sts token)'}
      </View>
      <View>消耗时间（含用户选择时间）{Math.floor(time / 1000)}s</View>
      <View
        // @ts-ignore
        x-for={item in urlList}
        // @ts-ignore
        x-if={urlList.length && item.value && item.status === 'fulfilled'}
      >
        {/* @ts-ignore */}
        <View key={item.value.url}>
          {/* @ts-ignore */}
          <a target='_blank' className='url-text' href={item.value.url}>{item.value.objectName}</a>
          {/* @ts-ignore */}
          <iframe src={item.value.url} name="preview" width={300} height={200} frameborder="0" className='iframe'></iframe>
        </View>
      </View>
    </View>
  );
}
