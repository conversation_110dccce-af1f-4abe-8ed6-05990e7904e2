import { createElement, useEffect, useRef, useState } from 'rax';
import RaxImage from 'rax-image';
import View from 'rax-view';
import './index.less';

export default function ImagePreview({ url }) {
  const [size, setSize] = useState({ width: 0, height: 0 });
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    if (url) {
      const img = new Image();
      img.onload = () => {
        setSize({ width: img.width, height: img.height });
      };
      img.src = url;
    }
  }, [url]);

  return (
    <View>
      <View className="image-size">尺寸：{size.width} * {size.height}</View>
      <RaxImage className="img" source={{ uri: url }} ref={imgRef}/>
    </View>
  );
}
