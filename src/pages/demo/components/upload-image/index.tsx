import { createElement, useCallback, useEffect, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { defaultConfig } from '../config/const';
import Config from '../config';
import fliggyImageUpload, {
  ImageUploadProps,
  deleteUrls,
} from '../../../../index';
import './index.less';
import ImagePreview from '../image-preview';

const getErrString = (err: any) => {
  if (typeof err === 'string') {
    return err;
  } else if (err instanceof Error) {
    return err.message;
  } else if (err?.errorMsg) {
    return err.errorMsg;
  } else if (err?.error) {
    return err.error;
  } else if (err?.errorMessage) {
    return err.errorMessage;
  } else if (err?.errMsg) {
    return err.errMsg;
  } else if (err?.err) {
    return err.err;
  } else if (err?.message) {
    return err.message;
  }
  return JSON.stringify(err);
};

export default function FliggyImageUploadDemo() {
  const [status, setStatus] = useState('');
  const [urlList, setUrlList] = useState([]);
  const [result, setResult] = useState([]);
  const [err, setErr] = useState('');
  const [loading, setLoading] = useState(false);
  const [time, setTime] = useState(0);
  const [config, setConfig] = useState<ImageUploadProps>(defaultConfig);
  const [showDesc, setShowDesc] = useState(false);

  const handleConfigChange = (newConfig) => {
    setConfig(newConfig);
  };

  useEffect(() => {
    // if(getApp) {
    //   getApp().__mtopEnv = 'wapa'
    // }
    // 获取url中的showDesc参数
    const urlParams = new URLSearchParams(location.search);
    const showDesc = urlParams.get('showDesc');
    setShowDesc(showDesc === 'true');
    //@ts-ignore
    window.deleteUrls = deleteUrls;
  }, []);

  const handleStatusChange = useCallback((status, urls) => {
    console.log('onStatusChange', status, urls);
    setUrlList(urls);
    setStatus(status);
  }, []);

  async function uploadImage() {
    setErr('');
    setUrlList([]);
    setResult([]);
    if (loading) {
      return;
    }

    try {
      setLoading(true);
      console.log('*****config', config);
      const consoleTimeFlag = `uploadImage${config.uploadConfig?.mode}`;
      console.time(consoleTimeFlag);
      const startTime = Date.now();
      const result = await fliggyImageUpload({
        ...config,
        onStatusChange: handleStatusChange,
      });
      console.timeEnd(consoleTimeFlag);
      const endTime = Date.now();
      setTime(endTime - startTime);
      setResult(result);
      console.log('*****result', result);
      setLoading(false);
    } catch (err) {
      console.log('***result_error', err);
      setErr(err);
      setResult([]);
      setLoading(false);
    }
  }

  return (
    <View className="upload-image-container">
      <Config
        onConfigChange={handleConfigChange}
        config={config}
        showDesc={showDesc}
      />
      <View className="container-button" onClick={uploadImage}>
        {loading ? `上传中` : `点击上传图片`}
      </View>
      <View className="time-text">
        消耗时间（含用户选择时间）{Math.floor(time / 1000)}s
      </View>
      {err && (
        <View className="err-wrap">
          <Text className="err-text">异常：{getErrString(err)}</Text>
        </View>
      )}
      <View x-for={item in urlList} className="image-wrap">
        <View key={item}>
          <a target="_blank" className="url-text" href={item?.url}>
            图片地址：{item?.url}
          </a>
          <View
            className="copy-wrap"
            onClick={() => {
              navigator.clipboard
                .writeText(item?.url)
                .then(() => {
                  alert('复制成功');
                })
                .catch((err) => {
                  alert('复制失败');
                });
            }}
          >
            复制地址
          </View>
          <View className="status-text">状态：{status}</View>
          {item?.error && (
            <View className="err-wrap">
              <Text className="err-text">{getErrString(item?.error)}</Text>
            </View>
          )}
          <ImagePreview url={item?.url} />
        </View>
      </View>
    </View>
  );
}
