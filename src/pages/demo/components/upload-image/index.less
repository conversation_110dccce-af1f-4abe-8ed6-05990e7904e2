@charset "UTF-8";

.upload-image-container {
  width: 100%;
  min-height: 2600rpx;  // 增大滚动区域，方式底部按钮被调试窗口挡住
  display: flex;
  flex-direction: column;
  align-items: center;
}

.container-button:active {
  background-color: #f2f2f2;
}
.container-button {
  width: 400rpx;
  height: 78rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(255, 224, 51);
  border-radius: 50rpx;
  margin: 20rpx 0;
  font-size: 28rpx;
}

.time-text {
  font-size: 24rpx;
  margin: 10rpx 0;
}

.err-text {
  font-size: 24rpx;
  color: red;
  margin: 10rpx 0;
}

.status-text {
  font-size: 24rpx;
  color: green;
  margin: 10rpx 0;
}

.url-text {
  font-size: 20rpx;
  max-width: 100vw;
  word-break: break-all;
  max-height: 100rpx;
  overflow: scroll;
}

.upload-status {
  background: #ccc;
}
.img,
.iframe {
  max-width: 300rpx;
  max-height: 200rpx;
  object-fit: contain;
  margin-top: 10rpx;
  border: 1rpx solid #ccc;
}

.h2 {
  padding: 2%;
  background: #ccc;
}
.component-container {
  padding: 2%;
  min-height: 750rpx;
  border: 1px solid #ccc;
}

.copy-wrap {
  width: 120rpx;
  height: 36rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(255, 224, 51);
  border-radius: 50rpx;
  border: 2rpx solid rgb(255, 224, 51);
  margin: 20rpx 0;
  font-size: 20rpx;
}
