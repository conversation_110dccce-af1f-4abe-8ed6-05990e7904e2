declare interface Window {
  /**
   * 支付宝桥
   */
  AlipayJSBridge: any;
  /**
   * WindVane容器提供的桥
   */
  WindVane: any;
  /**
   * 滴滴容器提供的桥
   */
  Fusion: any;
}

declare var require;
declare var wx;

// eslint-disable-next-line no-undef
declare interface PromiseConstructor {
  allSettled(
    promises: Array<Promise<any>>
  ): Promise<
    Array<{ status: "fulfilled" | "rejected"; value?: any; reason?: any }>
  >;
}
