/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import { isAlitrip } from '@ali/alitrip-env';
import { isHarmonyOS } from "@ali/rxpi-env"

import chooseImg from '../../logic/chooseImg/web';
import compressAccurately from '../../logic/compress/web';
import getBase64ByURL from '../../logic/urlConversion/web';
import {
  uploadByPutUrl,
  uploadByToken,
  uploadByBase64,
  isDataUrl,
  isFile,
} from '../../file-upload/index'
import { ImageUploadProps } from '../../interface';
import { STATUS_TYPE, ERROR_TYPE, UPLOAD_MODE } from '../../constants';
import { supportImgUpload, formatList, guid, allSettled } from '../../utils';
import {
  sendLogger,
  updateBaseInfo,
  getShortBase64,
  STAGE,
  STATUS,
} from '../../file-upload/logger/web';

const uploadImage = async (props: ImageUploadProps = {}) => {
  let {
    maxSelect = 1,
    camera = false,
    album = false,
    chooseByNative = false,
    taobaoBizName = '',
    openVerify = false,
    skipImageCompress = false,
    autoCompress = true,
    // imgCanvasOptions = {},
    quality,
    uploadConfig = {},
    enablePDF = false,
    onStatusChange,
  } = props;

  // uploadConfig.mode缺省，默认为ORIGIN； 鸿蒙系统降级为ORIGIN
  if (!uploadConfig.mode || isHarmonyOS) {
    uploadConfig.mode = UPLOAD_MODE.ORIGIN
  }

  if (uploadConfig.customBucket) {
    // 定制 bucket 上传（普通接入无需传入），需使用旧链路上传base64文件; 定制bucket的有第三方维护设置内容，不能保证兼容性，需维持原逻辑不变。
    uploadConfig.mode = UPLOAD_MODE.ORIGIN
  }


  const callId = guid();
  updateBaseInfo({
    callId,
    uploadMode: uploadConfig.mode,
  });
  sendLogger(
    {
      stage: STAGE.GET_FILE_START,
      inParams: props,
      status: STATUS.CONTINUE,
    },
    true,
  );

  let fileList = [];
  let fileStatusDetailList = [];
  let fileNameList = [];

  // 选择图片，拿到本地映射
  const chooseImageStartTime = Date.now();
  try {
    // 相机模式下，忽视maxSelect参数
    maxSelect = camera ? 1 : maxSelect

    // 非鸿蒙系统的低版本飞猪兼容 & 如果开启pdf 支持，强制降级到web原生上传
    try {
      if (isAlitrip) {
        chooseByNative = chooseByNative || (supportImgUpload() && !isHarmonyOS) || enablePDF;
      } else {
        chooseByNative = chooseByNative || enablePDF;
      }
    } catch (err) {
      console.error(`supportImgUpload failed: ${err.message}`);
      chooseByNative = chooseByNative || enablePDF;
    }
    fileList = await chooseImg({ maxSelect, enablePDF, camera, album, taobaoBizName }, chooseByNative, uploadConfig.mode === UPLOAD_MODE.ORIGIN);
    fileNameList = fileList.map(item => item.name || item);
    fileStatusDetailList = formatList(fileList)

    sendLogger({
      stage: STAGE.GET_FILE_END,
      outParams: { urlList: fileNameList },
      status: STATUS.SUCCESS,
      duration: Date.now() - chooseImageStartTime
    });
  } catch (err) {
    console.error('chooseImg', err);
    sendLogger({
      stage: STAGE.GET_FILE_END,
      inParams: { maxSelect },
      status: STATUS.FAILURE,
      duration: Date.now() - chooseImageStartTime
    });
    throw new Error(err);
    // throw err;
  }

  // 校验图片张数
  if (openVerify && maxSelect < fileList.length) {
    sendLogger({
      stage: STAGE.VERIFY,
      status: STATUS.FAILURE,
      error: ERROR_TYPE.LIMIT_EXCEEDED
    });
    throw ERROR_TYPE.LIMIT_EXCEEDED;
  }
  console.log('***获取图片 url', fileList)

  onStatusChange && onStatusChange(STATUS_TYPE.CHOOSE, fileStatusDetailList);

  const fileItem = fileList[0];
  const needUrlConversion = isFile(fileItem) === false && isDataUrl(fileItem) === false;
  const needCompress = skipImageCompress !== true && (quality || autoCompress);

  // 图片处理，根据本地映射生成base64，可设置图片宽高方向
  if (needUrlConversion) {
    const base64StartTime = Date.now();
    try {
      fileList = await getBase64ByURL(fileList, {});
      fileStatusDetailList = formatList(fileList)
      sendLogger({
        stage: STAGE.PROCESS_BASE64,
        outParams: {
          urlList: getShortBase64(fileList),
        },
        status: STATUS.SUCCESS,
        duration: Date.now() - base64StartTime
      });
    } catch (err) {
      sendLogger({
        stage: STAGE.PROCESS_BASE64,
        status: STATUS.FAILURE,
        error: err,
        duration: Date.now() - base64StartTime
      });
      throw new Error(err);
    }

    onStatusChange &&
      onStatusChange(STATUS_TYPE.URL_CONVERSION, fileStatusDetailList);
  }

  if (needCompress) {
    // 是否需要压缩，quality为压缩质量，autoCompress为智能压缩，将超过600k的图片压缩到600k以下。
    const compressStartTime = Date.now();
    const temp = fileList;
    try {
      fileList = await compressAccurately(fileList, {
        quality,
        imgCanvasOptions: {},
        autoCompress,
      });
      // 通过埋点发现有出现单项为空字符串的情况, 这里如果为空用原始值覆盖
      fileList = fileList.map((item, index) => item || temp[index]);
      fileStatusDetailList = formatList(fileList)
      sendLogger({
        stage: STAGE.PROCESS_COMPRESS,
        outParams: {
          urlList: fileNameList,
        },
        status: STATUS.SUCCESS,
        duration: Date.now() - compressStartTime
      });
    } catch (err) {
      // 压缩失败兜底原图上传
      sendLogger({
        stage: STAGE.PROCESS_COMPRESS,
        status: STATUS.FAILURE,
        duration: Date.now() - compressStartTime,
        error: err
      });
      fileList = temp;
      fileStatusDetailList = formatList(fileList)
    }
    onStatusChange && onStatusChange(STATUS_TYPE.COMPRESS, fileStatusDetailList);
    console.log('***处理图片 压缩 url', fileList)
  }

  // allSettled 不会出现异常, 上传图片
  let res
  const uploadStartTime = Date.now();
  switch (uploadConfig.mode) {
    case UPLOAD_MODE.OSS_TOKEN:
      res = await uploadByToken(fileList, uploadConfig, onStatusChange);
      break;
    case UPLOAD_MODE.OSS_URL:
      res = await uploadByPutUrl(fileList, uploadConfig, onStatusChange);
      break;
    case UPLOAD_MODE.ORIGIN:
    default:
      res = await uploadByBase64(fileList, uploadConfig, onStatusChange);
  }

  sendLogger({
    stage: STAGE.UPLOAD_RES,
    outParams: {
      res,
      uploadMode: uploadConfig.mode,
      duration: Date.now() - uploadStartTime
    },
    status: STATUS.SUCCESS,
    duration: Date.now() - uploadStartTime
  });
  return Promise.allSettled
    ? Promise.allSettled(res)
    : allSettled(res);
};

export default uploadImage;
