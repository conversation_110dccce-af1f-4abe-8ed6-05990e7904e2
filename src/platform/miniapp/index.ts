/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import chooseImg from '../../logic/chooseImg/miniapp';
import compressAccurately from '../../logic/compress/miniapp';
import getBase64ByURL from '../../logic/urlConversion/miniapp';
import upload from '../../logic/upload';
import { ImageUploadProps } from '../../interface';
import { STATUS_TYPE, ERROR_TYPE } from '../../constants';
import { formatList } from '../../utils';

const uploadImage = async (props: ImageUploadProps = {}) => {
  let {
    maxSelect = 1,
    quality,
    autoCompress = true,
    uploadConfig = {},
    openVerify = false,
    onStatusChange,
    camera,
    album
  } = props;
  let urlList = [];

  // 选择图片，拿到本地映射
  try {
    // 相机模式下，忽视maxSelect参数
    maxSelect = camera ? 1 : maxSelect

    urlList = await chooseImg({ maxSelect, camera, album });
  } catch (err) {
    console.error('chooseImg', err);
    throw err;
  }

  // 校验图片张数
  if (openVerify && maxSelect < urlList.length) {
    throw ERROR_TYPE.LIMIT_EXCEEDED;
  }

  onStatusChange && onStatusChange(STATUS_TYPE.CHOOSE, formatList(urlList));

  // 是否需要压缩，quality为压缩质量，autoCompress为自动压缩，默认设置为中等压缩。
  if (quality || autoCompress) {
    try {
      urlList = await compressAccurately(urlList, quality, autoCompress);
    } catch (err) {
      // 压缩失败兜底原图上传
      // urlList = urlList;
      console.error('compressAccurately', err);
    }
  }
  onStatusChange && onStatusChange(STATUS_TYPE.COMPRESS, formatList(urlList));

  // 图片处理，根据本地映射生成base64
  try {
    urlList = await getBase64ByURL(urlList);
  } catch (err) {
    console.error('getBase64ByURL', err);
    throw err;
  }
  onStatusChange &&
    onStatusChange(STATUS_TYPE.URL_CONVERSION, formatList(urlList));

  return await upload(urlList, uploadConfig, onStatusChange);
};
export default uploadImage;
