import Mtop from '@ali/rxpi-mtop';
import { CommonRequestConfig } from '../interface/mtop';

export function sendRequest<T>(config: CommonRequestConfig): Promise<T> {
  return new Promise((resolve, reject) => {
    Mtop.request(
      config,
      (res: T) => {
        // TODO: 这里不一定是响应成功了，需要判断下分片的响应是不是success
        resolve(res);
      },
      (error: Error) => {
        reject(error);
      },
      undefined,
    );
  });
}

export function uploadBaseUrl<T>(config: CommonRequestConfig): Promise<T> {
  return sendRequest(config);
}
