import { isRealWeChatMiniProgram, isWeChatMiniProgramH5 } from '@ali/rxpi-env';

// 上传图片限制的最大宽度
export const MAX_IMAGE_WIDTH = 2000;
// 压缩图片最大限制
export const COMPRESS_LIMIT_SIZE = 1 * 600;
// mtop上传最大限制
export const UPLOAD_LIMIT_SIZE = 1 * 500 * 1024;

const isWechat = isRealWeChatMiniProgram || isWeChatMiniProgramH5;

// faas 服务mtop接口
export const API_GATEWAY = isWechat
  ? 'mtop.trip.serverless.api.wx.gateway'
  : 'mtop.trip.serverless.api.gateway';

// faas 服务版本号
export const API_VERSION = isWechat ? '1.0' : '2.0';

// 各阶段类型枚举
export enum STATUS_TYPE {
  // 选择图片后
  CHOOSE = 'CHOOSED',
  // 压缩完成后
  COMPRESS = 'COMPRESSED',
  // 转base64后
  URL_CONVERSION = 'URL_CONVERSION',
  // upload中
  UPLOAD_ING = 'UPLOAD_ING',
  // 上传完成
  UPLOAD_END = 'UPLOAD_END',
}

// 上传图片模式
export enum UPLOAD_MODE {
  // 原模式
  ORIGIN = 'ORIGIN',
  // 使用 OSS_URL 模式
  OSS_URL = 'OSS_URL',
  // 使用 OSS_TOKEN 模式
  OSS_TOKEN = 'OSS_TOKEN',
}

// 错误异常枚举
export enum ERROR_TYPE {
  // 选择图片超过最大张数
  LIMIT_EXCEEDED = 'LIMIT_EXCEEDED',
  // 取消图片上传
  CHOOSE_CANCEL = 'CHOOSE_CANCEL',
  // 选择图片失败
  CHOOSE_FAIL = 'CHOOSE_FAIL',
  // 上传失败
  UPLOAD_FAIL = 'UPLOAD_FAIL',
  // 压缩失败
  COMPRESS_FAIL = 'COMPRESS_FAIL',
  // 转换base64失败
  CONVERT_BASE_FAIL = 'CONVERT_BASE_FAIL',
  // 上传时获取到的base64不存在
  BASE_NULL = 'BASE_NULL',
}
