export function guid(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
export function allSettled(arr) {
  return new Promise((resolve, reject) => {
    if (Object.prototype.toString.call(arr) !== '[object Array]') {
      return reject(
        new TypeError(
          `${typeof arr} ${arr} is not iterable(cannot read property Symbol(Symbol.iterator))`,
        ),
      );
    }
    const args = Array.prototype.slice.call(arr);
    if (args.length === 0) return resolve([]);
    let arrCount = args.length;

    function resolvePromise(index, value) {
      if (typeof value === 'object') {
        const { then } = value;
        if (typeof then === 'function') {
          then.call(
            value,
            (val) => {
              args[index] = { status: 'fulfilled', value: val };
              if (--arrCount === 0) {
                resolve(args);
              }
            },
            (e) => {
              args[index] = { status: 'rejected', reason: e };
              if (--arrCount === 0) {
                resolve(args);
              }
            },
          );
        }
      }
    }

    for (let i = 0; i < args.length; i++) {
      resolvePromise(i, args[i]);
    }
  });
}

// 判断飞猪版本
function compareVersion(relVersion: string, digit?: number): number {
  let version = '';
  const match = navigator.userAgent.match(/AliTrip[\s/][\d.]+/gim);

  if (match) {
    version = match[0].match(/[\d.]+/gim)[0];
  }

  if (!version) return -1;

  for (
    let v = version.split('.'),
      r = relVersion.split('.'),
      d = digit || v.length,
      i = -1;
    ++i < d;

  ) {
    const _v = Number(v[i]) || 0;
    const _r = Number(r[i]) || 0;

    if (_r < _v) return 1;
    if (_v < _r) return -1;
  }

  return 0;
}

// 判断飞猪版本是否小于995，小于的话则不支持桥方法上传
export function supportImgUpload(): boolean {
  const result = compareVersion('9.9.5') || 0;
  return result < 0;
}

// 统一结构
export function formatList(list) {
  return list.map((val) => {
    const url = isFile(val) ? URL.createObjectURL(val) : val
    return {
      url,
      localUrl: url,
    };
  });
}


export function isFile(input) {
  return input instanceof File;
}