import req from 'require-esm';
import { isWeb } from 'universal-env';
export * from './interface/index'; // 导出types

export const upload = req(require('./logic/upload'));
export const chooseImg = isWeb
  ? req(require('./logic/chooseImg/web'))
  : req(require('./logic/chooseImg/miniapp'));
export const compressAccurately = isWeb
  ? req(require('./logic/compress/web'))
  : req(require('./logic/compress/miniapp'));
export const getBase64ByURL = isWeb
  ? req(require('./logic/urlConversion/web'))
  : req(require('./logic/urlConversion/miniapp'));

// 导出 file-upload 中的通用文件上传方法
export const uploadByBase64 = isWeb
  ? require('./file-upload/index').uploadByBase64
  : null;
export const uploadByToken = isWeb
  ? require('./file-upload/index').uploadByToken
  : null;
export const uploadByPutUrl = isWeb
  ? require('./file-upload/index').uploadByPutUrl
  : null;
export const deleteUrls = isWeb
  ? require('./file-upload/index').deleteUrls
  : null;

export default isWeb
  ? req(require('./platform/web/index'))
  : req(require('./platform/miniapp/index'));
