import putFiles from '../../../file-upload/uploadByToken';
import { ERROR_TYPE } from '../../../constants';
import { allSettled } from '../../../utils';


type GetFileOptions = {
  maxSelect: number;
  camera: boolean;
  inputAccept?: string;
}

type UploadProps = {
  maxSelect?: number;
  camera?: boolean;
  onStatusChange?: (status: string) => void;
  uploadConfig?: any;
  openVerify?: boolean;
  inputAccept?: string;
}
/**
 * 通过原生input拿文件
 */
export const getFile = ({
  maxSelect,
  camera,
  inputAccept = 'video/*',
}: GetFileOptions): Promise<File[]> =>
  new Promise((resolve, reject) => {
    const touchTapTime = new Date().valueOf();
    const fileInputElement = document.createElement(
      'INPUT',
    ) as HTMLInputElement;
    if (maxSelect > 1) {
      fileInputElement.multiple = true;
    }
    fileInputElement.type = 'file';
    camera && (fileInputElement.capture = "camera");

    fileInputElement.accept = inputAccept;

    //  处理取消逻辑
    let isCancel = true;

    function onWindowFocus() {
      // 如果事件距离触发时间小于 50ms 我们认为是原生事件触发，非用户触发 直接return 主要解决 onTouchTap 直接触发touchEnd 的问题
      if (new Date().valueOf() - touchTapTime < 50) {
        console.log('由原生onTouchTap 事件触发');
        return;
      }
      setTimeout(() => {
        if (isCancel) {
          removeListener();
          reject(ERROR_TYPE.CHOOSE_CANCEL);
        }
      }, 1000);
    }

    // 清空取消事件监听
    function removeListener() {
      window.removeEventListener('touchend', onWindowFocus);
      window.removeEventListener('focus', onWindowFocus);
      document.removeEventListener('WV.Resume', onWindowFocus);
    }
    //  由于部分浏览器上 touchTap 会直接触发 touchEnd 事件，所以 我们这里不加once参数
    window.addEventListener('touchend', onWindowFocus);
    window.addEventListener('focus', onWindowFocus, { once: true });
    document.addEventListener('WV.Resume', onWindowFocus, { once: true });

    fileInputElement.addEventListener('change', function () {
      // 取消相关逻辑
      isCancel = false;
      removeListener();

      // 获取文件流
      const fileList = Array.from(this.files);
      resolve(fileList)
    });
    // 需要插入一个不渲染的的dom
    // @ts-ignore Cannot assign to style because it is a read-only property
    fileInputElement.style = 'display:none';
    // 或者可以使用这个
    // fileInputElement.setAttribute('style', 'display:none');
    document.body.appendChild(fileInputElement);
    fileInputElement.click();
  });



const upload = async (props: UploadProps = {}) => {
  
  let {
    maxSelect = 1,
    camera = false,
    uploadConfig = {},
    openVerify = false,
    inputAccept = 'video/*',
  } = props;
  let files = [];

  
  try {
    // 相机模式下，忽视maxSelect参数
    maxSelect = camera ? 1 : maxSelect
    files = await getFile({ maxSelect, camera, inputAccept });
  } catch (err) {
    console.error('chooseImg', err);
    throw new Error(err);
    // throw err;
  }

  // 校验图片张数
  if (openVerify && maxSelect < files.length) {
    throw ERROR_TYPE.LIMIT_EXCEEDED;
  }
  console.log('***获取Files', files)

  const promiseList = await putFiles(files, uploadConfig)

  return Promise.allSettled
    ? Promise.allSettled(promiseList)
    : allSettled(promiseList);

};

export default upload;
