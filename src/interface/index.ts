import { Image2CanvasConfig } from './compress';
import { CommonRequestConfig, CommonResponse } from './mtop';
import { STATUS_TYPE, UPLOAD_MODE, ERROR_TYPE } from '../constants';

export {
  Image2CanvasConfig,
  CommonRequestConfig,
  CommonResponse,
  STATUS_TYPE,
  UPLOAD_MODE,
  ERROR_TYPE
}

export interface ImageUploadProps extends ChooseImgFuncProps {
  /**
   * 是否跳过图片压缩，如果跳过，压缩相关props 将无效
   */
  skipImageCompress?: boolean;
  /**
   * 图片转canvas参数，支持设置宽高，缩放比例，方向，只支持h5
   */
  // imgCanvasOptions?: Image2CanvasConfig;
  /**
   * 压缩质量
   */
  quality?: number;
  /**
   * 是否自动压缩，默认开启，如果不是特殊场景，建议默认开启
   */
  autoCompress?: boolean;
  /**
   * 上传参数
   */
  uploadConfig?: UploadConfigProps;
  /**
   * 上传图片进程中的钩子
   */
  onStatusChange?: OnStatusChange;
  /**
   * 选择图片是否走h5
   */
  chooseByNative?: boolean;
  /**
   * 开启校验，例如图片张数的校验
   */
  openVerify?: boolean;
}

export type SubDomain = 'm' | 'wapa' | 'waptest';

export interface UploadConfigProps {
  /**
   * 上传方式
   */
  mode?: UPLOAD_MODE;
  /**
   * 文件前缀
   */
  prefix?: string;
  /**
   * 可见时间
   */
  expires?: string | number;
  /**
   * 图像处理指令,比如加水印
   */
  process?: string;
  /**
   * 自定义上传bucket
   */
  customBucket?: string;
  /**
   * 上传接口前置回调，支持改造接口
   */
  onBeforeRequest?: (
    baseUrl: string,
    isFragmentation?: boolean,
  ) => CommonRequestConfig;
  /**
   * 上传接口后置回调，支持改造接口
   */
  onAfterRequest?: (result: CommonResponse) => any;
  /**
 * 仅web支持； 同 CommonRequestConfig 中的subDomain, 用于支持配置mtop环境
 * 二级域，当前环境（日常 'waptest'、预发 'wapa'、生产 'm'），默认'm'
 */
  subDomain?: SubDomain;
}

export interface ChooseImgFuncProps {
  /**
   * 最多选择图片数，等于1的时候为单选，大于1为多选，默认为1
   */
  maxSelect?: number;
  /**
   * 支持pdf
   */
  enablePDF?: boolean;
  /**
   * 是否走纯拍照模式，默认是false，如果走拍照，会忽视掉maxSelect参数
   */
  camera?: boolean;
  /**
   * 是否走纯相册模式，默认是false
   */
  album?: boolean;
  /**
   * 在淘宝app中使用uni-api获取图片需传入的bizName, 需要上游业务单独申请并传入，不传将降级为h5-input元素获取
   */
  taobaoBizName?: string;
}

/**
 * 上传方法的参数
 */
export interface UploadParam {
  baseString: string;
  getRequestConfig: any;
  onAfterRequest?: (result: CommonResponse) => any;
  onUploadStatusChange?: (
    value: Partial<StatusDetail>,
    index?: number,
  ) => void;
  currentIndex?: number;
}

/**
 * 结果类型
 */
export type StatusDetail = {
  /**
   * 图片地址，获取远程地址前，值为本地图片地址，获取远程地址后，值为远程地址
   */
  url: string;
  /**
   * 本地图片地址，用于上传成功前预览图片
   */
  localUrl: string;
  /**
   * 隐私图片objectName, 请求获取后才有
   */
  objectName?: string;
  /**
   * 结果
   * 上传完后才有，标识上传结果
   */
  success?: boolean;  
  /**
   * 错误信息
   * 上传完后才有，显示失败原因
   */
  error?: string;
};

export type OnStatusChange =  (type: STATUS_TYPE, details: StatusDetail[]) => void