export interface CommonMtopRequestConfigProps {
  /**
   * 返回数据类型，默认为 originaljson(p)
   */
  type?: string;
  /**
   * 是否使用MTop SDK代理，默认true
   */
  useNative?: boolean;
  /**
   * 请求方法（get|post），默认get
   */
  method?: string;
  /**
   * 设置超时（单位ms），默认20000
   */
  timeout?: number;
  /**
   * setRequestHeader， 禁止使用 HTTP Header 标准key 和 以 x-(mtop平台专用) 开头的key，建议使用 B-前缀(buss)：B-Foo
   */
  headers?: any;
}

export interface CommonRequestConfig {
  /**
   * mtop api
   */
  api: string;
  /**
   * mtop 版本号
   */
  v?: string;
  /**
   * web下使用；
   * 请求是使用 'GET' 还是 'POST'（WAP 下通过 CORS 实现），默认'GET'
   */
  type?: 'GET' | 'POST';
  /**
   * 支端、微信小程序使用；
   * 请求是使用 'GET' 还是 'POST'（WAP 下通过 CORS 实现），默认'GET'
   */
  method?: 'GET' | 'POST';
  /**
   * mtop 版本号 v为空的时候生效
   */
  version?: string;
  /**
   * 业务数据
   */
  data?: any;
  /**
   * ttid透传
   */
  ttid?: string;
  /**
   * mtop发送的header中的数据
   */
  headers?: any;
  /**
   * header为空时，由该字段指定
   */
  ext_headers?: any;
  /**
 * 仅web支持；
 * 二级域，当前环境（日常 'waptest'、预发 'wapa'、生产 'm'），默认'm'
 */
  subDomain?: 'm' | 'wapa' | 'waptest';
  /**
   * 请求控制项
   */
  config?: CommonMtopRequestConfigProps;
}
export interface CommonResponse {
  /**
   * mtop api
   */
  api: string;
  /**
   * 返回数据
   */
  data: {
    result?: {
      /**
       * 分片上传接口返回地址
       */
      url?: string;
      result?: {
        /**
         * 整段上传接口返回地址
         */
        url?: string;
      };
    };
  };
  /**
   * mtop 版本号
   */
  v: string;
}
