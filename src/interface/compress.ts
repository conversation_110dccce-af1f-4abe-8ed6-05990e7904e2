/**
 * 图片类型
 */
export type EImageType = 'image/png' | 'image/jpeg' | 'image/gif';

/**
 * 通用基类
 */
export interface IBaseConfig {
  [key: string]: any;
}

/**
 * 图片转canvas可设置的参数
 */
export interface Image2CanvasConfig extends IBaseConfig {
  /**
   * canvas图像的宽度，默认为image的宽度
   */
  width?: number;
  /**
   * canvas图像的高度，默认为image的高度
   */
  height?: number;
  /**
   * 相对于image的缩放比例，范围0-10，默认不缩放
   */
  scale?: number;
  /**
   * 参数旋转方向
   * 1-0°
   * 2-水平翻转
   * 3-180°
   * 4-垂直翻转
   * 5-顺时针90°+水平翻转
   * 6-顺时针90°
   * 7-顺时针90°+垂直翻转
   * 8-逆时针90°
   */
  orientation?: number;
}
