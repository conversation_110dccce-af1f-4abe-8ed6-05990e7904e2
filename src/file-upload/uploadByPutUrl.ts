/**
 * put url 方式简单上传文件，不依赖ali-oss  sdk, 适用于图片及其他小文件
 */
import { STAGE, STATUS, sendLogger } from './logger/web';
import getTokenUrl, { UrlsItem } from './utils/getTokenUrls';
import { FileInfo } from './types';
import { getFileInfo } from './utils/index';
import { UPLOAD_MODE } from './constants';
import { OnStatusChange, STATUS_TYPE, StatusDetail } from '../interface';
import { formatList } from '../utils';

export interface PutImageOptions {
  url: string;
  dataUrl: string;
}

const uploadWithFetch = async (info: FileInfo, urlInfo: UrlsItem, startTime) => {
  const { put: putUrl, get: getUrl, putHeaders } = urlInfo || {}
  const innerStartTime = Date.now()
  try {
    // 上传图片
    const response = await fetch(putUrl+'&getAvatar=1', {
      method: 'PUT',
      headers: putHeaders,
      body: info.data,
    });

    const duration = Date.now() - startTime
    const partDuration = Date.now() - innerStartTime
    const fileSizeInfo = info.size
    const fileSize = fileSizeInfo.size

    // 检查状态码是否为200，表示成功
    if (response.status === 200) {
      console.log('上传成功');
      sendLogger({
        stage: STAGE.UPLOAD_END,
        outParams: {
          duration,
          partDuration,
          fileUrl: getUrl,
          fileSize,
          fileSizeUnit: fileSizeInfo.unit,
          fileMime: info.mime,
          uploadMode: UPLOAD_MODE.OSS_TOKEN,
        },
        status: STATUS.SUCCESS,
        duration,
        partDuration,
        fileSize,
      });
      // 返回数据和原通道一致
      return {
        url: getUrl,
        objectName: getUrl.split('?')[0],
      }
    } else {
      // 如果状态码不是200，则抛出错误
      throw new Error(`fetch HTTP error! status: ${response.status}`);
    }
  } catch (error) {
    console.error('上传失败:', error);
    const duration = Date.now() - startTime
    const partDuration = Date.now() - innerStartTime
    sendLogger({
      stage: STAGE.UPLOAD_END,
      outParams: {
        duration,
        partDuration,
        fileUrl: getUrl,
      },
      status: STATUS.FAILURE,
      duration,
      partDuration,
      error,
    });
    throw error; // 可以选择是否要在这里再次抛出错误，或者处理错误
  }
}

const uploadWithXHR = async (info: FileInfo, urlInfo: UrlsItem, startTime) => {
  return new Promise((resolve, reject) => {
    const { put: putUrl, get: getUrl, putHeaders } = urlInfo || {}
    const innerStartTime = Date.now()
    const xhr = new XMLHttpRequest();
    xhr.open('PUT', putUrl+'&getAvatar=1', true);
    Object.keys(putHeaders).forEach(key => {
      xhr.setRequestHeader(key, putHeaders[key]);
    });

    const fileSizeInfo = info.size
    const fileSize = fileSizeInfo.size

    function onError(error) {
      console.error('上传失败:', error);
      const duration = Date.now() - startTime
      const partDuration = Date.now() - innerStartTime
      sendLogger({
        stage: STAGE.UPLOAD_END,
        outParams: {
          duration,
          partDuration,
          fileUrl: getUrl,
        },
        status: STATUS.FAILURE,
        duration,
        partDuration,
        error,
      });
      reject(error)
    };

    xhr.onload = function () {
    const duration = Date.now() - startTime
    const partDuration = Date.now() - innerStartTime

      if (xhr.status === 200) {
        console.log('上传成功');
        sendLogger({
          stage: STAGE.UPLOAD_END,
          outParams: {
            duration,
            partDuration,
            fileUrl: getUrl,
            fileSize,
            fileSizeUnit: fileSizeInfo.unit,
            fileMime: info.mime,
            uploadMode: UPLOAD_MODE.OSS_TOKEN,
          },
          status: STATUS.SUCCESS,
          duration,
          partDuration,
          fileSize,
        });
        // 返回数据和原通道一致
        resolve({
          url: getUrl,
          objectName: getUrl.split('?')[0],
        })
      } else {
        // 如果状态码不是200，则抛出错误
        const err = new Error(`xhr HTTP error! status:
        ${xhr.status}`);
        onError(err);
      }
    };
    xhr.onerror = onError
    xhr.send(info.data);
  })
}


const uploadWithRetry = async (file, urlInfo: UrlsItem, startTime) => {
  // uploadWithFetch 失败后 uploadWithXHR重试; to fix 支付宝fetch报错问题
  return uploadWithFetch(file, urlInfo, startTime).catch(() => uploadWithXHR(file, urlInfo, startTime))
}

export default async function putFiles(files: (File | string)[], uploadConfig, onStatusChange?: OnStatusChange) {
  sendLogger({
    stage: STAGE.UPLOAD_START,
    status: STATUS.CONTINUE,
  });
  const startTime = Date.now()

  // 处理回调
  const fileStatusDetailList = formatList(files)
  let count = 0
  const onUploadStatusChange = (value: Partial<StatusDetail>, currentIndex: number) => {
    count++;
    fileStatusDetailList[currentIndex] = {
      ...fileStatusDetailList[currentIndex],
      ...value,
    };

    const status = count === files.length ? STATUS_TYPE.UPLOAD_END : STATUS_TYPE.UPLOAD_ING
    onStatusChange && onStatusChange(status, fileStatusDetailList);
  };

  // 获取fileInfo 
  const fileInfos: FileInfo[] = files.map(file => getFileInfo(file))

  let mimes = fileInfos.map(info => info.mime)
  // 获取 oss 上传 url
  let getTokenUrlResResult
  const innerStartTime = Date.now()
  try {
    const getUrlRes = await getTokenUrl({
      mimes,
      uploadNumber: files.length,
      ...(uploadConfig || {}),
    })

    const getTokenUrlResData = getUrlRes.data
    if (getTokenUrlResData?.success === false) {
      throw new Error(`getTokenUrl err: ${getTokenUrlResData?.msg}`)
    }

    getTokenUrlResResult = getTokenUrlResData.result
    if (getTokenUrlResResult?.status === 'failure') { // 只有失败情况返回 status, 成功时不返回
      throw new Error(`getTokenUrl err: ${getTokenUrlResResult?.msg}`)
    }
    sendLogger({
      stage: STAGE.UPLOAD_GET_TOKEN_URL,
      status: STATUS.SUCCESS,
      duration: Date.now() - startTime,
      partDuration: Date.now() - innerStartTime,
    });
  } catch (e) {
    sendLogger({
      stage: STAGE.UPLOAD_GET_TOKEN_URL,
      status: STATUS.FAILURE,
      duration: Date.now() - startTime,
      partDuration: Date.now() - innerStartTime,
      error: e
    });
    console.log('****getTokenUrl err', e)
    fileStatusDetailList.forEach((item, index) => (
      fileStatusDetailList[index] = {
        ...item,
        success: false,
        error: e
      }
    ))
    onStatusChange && onStatusChange(STATUS_TYPE.UPLOAD_END, fileStatusDetailList)
    throw e
  }

  const { urls } = getTokenUrlResResult

  return fileInfos.map(async (info, index) => {
    return uploadWithRetry(info, urls[index], startTime).then((res: { url: string, objectName: string }) => {
      onUploadStatusChange({
        url: res?.url,
        objectName: res?.objectName,
        success: true,
      }, index)
      return res
    }).catch((err) => {
      onUploadStatusChange({
        success: false,
        error: err,
      }, index)
      throw err
    })
  })
}

