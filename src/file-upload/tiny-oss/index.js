import ajax from './utils/ajax';
import {
  unix,
  blobTo<PERSON>uffer,
  assertOptions,
  getContentMd5,
  getSignature,
} from './utils';

export default class TinyOSS {
  constructor(options = {}) {
    assertOptions(options);

    this.opts = Object.assign({
      region: 'oss-cn-hangzhou',
      internal: false,
      cname: false,
      secure: false,
      timeout: 60000,
    }, options);

    const {
      bucket,
      region,
      endpoint,
      internal,
    } = this.opts;

    this.host = '';

    if (endpoint) {
      this.host = endpoint?.split('://')[1];
    } else {
      let host = bucket;
      if (internal) {
        host += '-internal';
      }
      host += `.${region}.aliyuncs.com`;
      this.host = host;
    }
  }

  put(objectName, blob, options = {}) {
    return new Promise((resolve, reject) => {
      blobToBuffer(blob)
        .then((buf) => {
          const {
            accessKeyId,
            accessKeySecret,
            stsToken,
            bucket,
          } = this.opts;
          const verb = 'PUT';
          const contentMd5 = getContentMd5(buf);
          const contentType = blob.type;
          const headers = {
            'Content-Md5': contentMd5,
            'Content-Type': contentType,
            'x-oss-date': new Date().toGMTString(),
            ...options.headers || {},
          };

          if (stsToken) {
            headers['x-oss-security-token'] = stsToken;
          }

          const signature = getSignature({
            verb,
            contentMd5,
            headers,
            bucket,
            objectName,
            accessKeyId,
            accessKeySecret,
          });

          headers.Authorization = `OSS ${accessKeyId}:${signature}`;
          const protocol = this.opts.secure ? 'https' : 'http';
          const url = `${protocol}://${this.host}/${objectName}`;

          return ajax(url, {
            method: verb,
            headers,
            data: blob,
            timeout: this.opts.timeout,
            onprogress: options.onprogress,
          });
        })
        .then(resolve)
        .catch(reject);
    });
  }

  // https://help.aliyun.com/document_detail/45126.html
  putSymlink(objectName, targetObjectName) {
    const {
      accessKeyId,
      accessKeySecret,
      stsToken,
      bucket,
    } = this.opts;
    const verb = 'PUT';
    const headers = {
      'x-oss-date': new Date().toGMTString(),
      'x-oss-symlink-target': encodeURI(targetObjectName),
    };

    if (stsToken) {
      headers['x-oss-security-token'] = stsToken;
    }

    const signature = getSignature({
      verb,
      headers,
      bucket,
      objectName,
      accessKeyId,
      accessKeySecret,
      subResource: {
        symlink: '',
      },
    });

    headers.Authorization = `OSS ${accessKeyId}:${signature}`;
    const protocol = this.opts.secure ? 'https' : 'http';
    const url = `${protocol}://${this.host}/${objectName}?symlink`;

    return ajax(url, {
      method: verb,
      headers,
      timeout: this.opts.timeout,
    });
  }

  signatureUrl(objectName, options = {}) {
    const {
      expires = 1800,
      method,
      process,
      response,
    } = options;
    const {
      accessKeyId,
      accessKeySecret,
      stsToken,
      bucket,
    } = this.opts;
    const headers = {};
    const subResource = {};

    if (process) {
      const processKeyword = 'x-oss-process';
      subResource[processKeyword] = process;
    }

    if (response) {
      Object.keys(response).forEach((k) => {
        const key = `response-${k.toLowerCase()}`;
        subResource[key] = response[k];
      });
    }

    Object.keys(options).forEach((key) => {
      const lowerKey = key.toLowerCase();
      const value = options[key];
      if (lowerKey.indexOf('x-oss-') === 0) {
        headers[lowerKey] = value;
      } else if (lowerKey.indexOf('content-md5') === 0) {
        headers[key] = value;
      } else if (lowerKey.indexOf('content-type') === 0) {
        headers[key] = value;
      } else if (lowerKey !== 'expires' && lowerKey !== 'response' && lowerKey !== 'process' && lowerKey !== 'method') {
        subResource[lowerKey] = value;
      }
    });

    const securityToken = options['security-token'] || stsToken;
    if (securityToken) {
      subResource['security-token'] = securityToken;
    }

    const expireUnix = unix() + expires;
    const signature = getSignature({
      type: 'url',
      verb: method || 'GET',
      accessKeyId,
      accessKeySecret,
      bucket,
      objectName,
      headers,
      subResource,
      expires: expireUnix,
    });
    const protocol = this.opts.secure ? 'https' : 'http';
    let url = `${protocol}://${this.host}/${objectName}`;
    url += `?OSSAccessKeyId=${accessKeyId}`;
    url += `&Expires=${expireUnix}`;
    url += `&Signature=${encodeURIComponent(signature)}`;
    Object.keys(subResource).forEach((k) => {
      url += `&${k}=${encodeURIComponent(subResource[k])}`;
    });

    return url;
  }
}
