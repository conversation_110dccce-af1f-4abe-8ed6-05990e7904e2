import SlsTracker from '@aliyun-sls/web-track-browser';
import User from '@ali/rxpi-user';

type BaseInfo = {
  callId?: string;
  src: string;
  page: string;
  ua: string;
  uploadMode?: string;
}

const opts = {
  host: 'cn-shanghai.log.aliyuncs.com', // 所在地域的服务入口。
  project: 'fliggy-file-upload', // Project名称。
  logstore: 'fliggy-file-upload-logstore', // Log store名称。
  time: 5, // 发送日志的时间间隔，默认是10秒。
  count: 10, // 发送日志的数量大小，默认是10条。
  // topic: "test", // 自定义日志主题。
  // source: "fl-images-sdk",
  // tags: {
  //   tags: "testTags",
  // },
};

const tracker = new SlsTracker(opts);

const baseInfo: BaseInfo = {
  src: location?.href || 'unknown',
  page: location?.hostname + location?.pathname || 'unknown',
  ua: navigator?.userAgent || '',
};
let userInfo: object | null = null;

const enum STAGE {
  // getFile
  GET_FILE_START = 'getFileStart',
  GET_FILE_END = 'getFileEnd',

  // Verify
  VERIFY = 'verify',

  // PROCESS
  PROCESS_BASE64 = 'base64',
  PROCESS_COMPRESS = 'compress',
  PROCESS_EDIT = 'edit',

  // UPLOAD
  UPLOAD_START = 'uploadStart',
  UPLOAD_GET_TOKEN_URL = 'UploadGetTokenUrl',
  UPLOAD_SIGNATURE = 'UploadSignature',
  UPLOAD_GET_INFO = 'UploadGetInfo',
  UPLOAD_END = 'UploadEnd',
  UPLOAD_RES = 'UploadRes',
}


const enum STATUS {
  CONTINUE = 'Continue',
  SUCCESS = 'Success',
  FAILURE = 'Failure',
}

let getUserInfoPromise: Promise<any> | null = null;

function getUserInfo() {
  return new Promise((resolve) => {
    User.getUserInfo((data) => {
      resolve(data);
    });
  });
}
function updateBaseInfo(info: Partial<BaseInfo>): void {
  Object.assign(baseInfo, info);
}

function sendLogger(content: object, sendUserInfo = false): void {
  if (!userInfo && sendUserInfo) {
    if (!getUserInfoPromise) {
      getUserInfoPromise = getUserInfo();
    }
    getUserInfoPromise.then((res) => {
      userInfo = res.info;
      tracker.send(Object.assign({}, baseInfo, userInfo, content));
    });
  } else {
    tracker.send(Object.assign({}, baseInfo, content));
  }
}

function getShortBase64(urlList: string[]) {
  return urlList.map((item) => ({
    shortBase64: item.substring(0, 30),
    length: item.length,
  }));
}

export { sendLogger, updateBaseInfo, STAGE, STATUS, getShortBase64 };
