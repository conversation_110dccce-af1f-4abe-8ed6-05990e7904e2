import { isRealWeChatMiniProgram, isWeChatMiniProgramH5 } from '@ali/rxpi-env';
const isWechat = isRealWeChatMiniProgram || isWeChatMiniProgramH5;

// faas 服务mtop接口
export const API_GATEWAY = isWechat
  ? 'mtop.trip.serverless.api.wx.gateway'
  : 'mtop.trip.serverless.api.gateway';

// faas 服务版本号
export const API_VERSION = isWechat ? '1.0' : '2.0';

// 日志收集到 https://arms.console.aliyun.com/retcode/?spm=5176.arms.0.0.6e34f167uWOP3W&pid=haa3j9plpi@99183ca39d2ca5c#/index
export const LOG_PID = 'haa3j9plpi@99183ca39d2ca5c';
export const LOG_URL = 'https://arms-retcode.aliyuncs.com/r.png';

export enum UPLOAD_MODE {
  // 原模式
  ORIGIN = 'ORIGIN',
  // 使用 OSS_URL 模式
  OSS_URL = 'OSS_URL',
  // 使用 OSS_TOKEN 模式
  OSS_TOKEN = 'OSS_TOKEN',
}

export enum UPLOAD_STEP {
  CHOOSE = 'CHOOSE',
  TRANS2BASE64 = 'TRANS2BASE64',
  COMPRESS='COMPRESS',
  UPLOAD = 'UPLOAD',
}