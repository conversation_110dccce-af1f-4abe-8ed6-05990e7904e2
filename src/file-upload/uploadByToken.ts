import OSS from './tiny-oss';
import getTokenUrl from './utils/getTokenUrls';
import { STAGE, STATUS, sendLogger } from './logger/web';
import { UPLOAD_MODE } from './constants';
import { getFileInfo } from './utils/index';
import { FileInfo } from './types';
import { OnStatusChange, STATUS_TYPE, StatusDetail } from '../interface';
import { formatList } from '../utils';

let currentOssClient

// 获取当前的 oss client, 可用于调用client其他api
export function getOssClient() {
  return currentOssClient
}

export default async function putFiles(files: (File | string)[], uploadConfig, onStatusChange?: OnStatusChange) {
  sendLogger({
    stage: STAGE.UPLOAD_START,
    status: STATUS.CONTINUE,
  });
  const startTime = Date.now()

  // 处理回调
  const fileStatusDetailList = formatList(files)
  let count = 0
  const onUploadStatusChange = (value: Partial<StatusDetail>, currentIndex: number) => {
    count++;
    fileStatusDetailList[currentIndex] = {
      ...fileStatusDetailList[currentIndex],
      ...value,
    };

    const status = count === files.length ? STATUS_TYPE.UPLOAD_END : STATUS_TYPE.UPLOAD_ING
    onStatusChange && onStatusChange(status, fileStatusDetailList);
  };

  // 获取fileInfo 
  const fileInfos: FileInfo[] = files.map(file => getFileInfo(file))

  let mimes = fileInfos.map(info => info.mime)
  let getTokenUrlResResult
  const isPrivateFile = !!uploadConfig?.expires
  try {
    const getTokenUrlRes = await getTokenUrl({
      mimes,
      requestType: 'token',
      uploadNumber: files.length,
      ...(uploadConfig || {}),
    })

    const getTokenUrlResData = getTokenUrlRes.data
    if (getTokenUrlResData?.success === false) {
      throw new Error(`getTokenUrl err: ${getTokenUrlResData?.msg}`)
    }

    getTokenUrlResResult = getTokenUrlResData?.result
    if (getTokenUrlResResult?.status === 'failure') {
      throw new Error(getTokenUrlResResult?.message)
    }
    sendLogger({
      stage: STAGE.UPLOAD_GET_TOKEN_URL,
      status: STATUS.SUCCESS,
      duration: Date.now() - startTime,
    });
  } catch (e) {
    sendLogger({
      stage: STAGE.UPLOAD_GET_TOKEN_URL,
      status: STATUS.FAILURE,
      duration: Date.now() - startTime,
      error: e
    });
    fileStatusDetailList.forEach((item, index) => (
      fileStatusDetailList[index] = {
        ...item,
        success: false,
        error: e
      }
    ))
    onStatusChange && onStatusChange(STATUS_TYPE.UPLOAD_END, fileStatusDetailList)
    throw e
  }


  const { credentials, clientInfo, willUploadResPathNamesMaps } = getTokenUrlResResult

  const OSSParams: OSS.Options = {
    secure: true,
    bucket: clientInfo.bucket,
    region: clientInfo.region,
    accessKeyId: credentials.accessKeyId,
    accessKeySecret: credentials.accessKeySecret,
    stsToken: credentials.securityToken,
  }

  if (isPrivateFile) {
    OSSParams.cname = clientInfo.cname;
    OSSParams.endpoint = clientInfo.endpoint;
  }

  const client = new OSS(OSSParams);
  currentOssClient = client


  return fileInfos.map((info, index) => {
    const { willUploadResPathName, putHeaders } = willUploadResPathNamesMaps[index] || {}
    const signatureStartTime = Date.now()
    // @ts-ignore 隐私图片返回加签地址；公共图片无须加签，加上getAvatar=1支持IOS淘宝正常显示图片
    try {
      const getUrl = isPrivateFile ? client.signatureUrl(willUploadResPathName, { expires: uploadConfig.expires, method: 'GET' }) : `https://fli.alicdn.com/${willUploadResPathName}?getAvatar=1`
      sendLogger({
        stage: STAGE.UPLOAD_SIGNATURE,
        status: STATUS.SUCCESS,
        duration: Date.now() - signatureStartTime,
      });

      return client.put(willUploadResPathName, info.data, {
        headers: putHeaders,
        /** 回调 参考：https://help.aliyun.com/zh/oss/user-guide/upload-callbacks-12#concept-ywd-dlb-5db 
         *  callback: {
          // 设置回调请求的服务器地址，例如http://oss-demo.aliyuncs.com:23450。
          url: 'http://oss-demo.aliyuncs.com:23450',
          //（可选）设置回调请求消息头中Host的值，即您的服务器配置Host的值。
          //host: 'yourCallbackHost',
          // 设置发起回调时请求body的值。
          body: 'bucket=${bucket}&object=${object}&var1=${x:var1}&var2=${x:var2}',
          // 设置发起回调请求的Content-Type。
          contentType: 'application/x-www-form-urlencoded',
          // 设置发起回调请求的自定义参数。
          customValue: {
            var1: 'value1',
            var2: 'value2'
          }
        }
        */
      }).then(async (res) => {
        const fileSizeInfo = info.size
        const duration = Date.now() - startTime
        const fileSize = fileSizeInfo.size
        sendLogger({
          stage: STAGE.UPLOAD_END,
          outParams: {
            duration,
            fileUrl: getUrl,
            fileSize,
            fileSizeUnit: fileSizeInfo.unit,
            fileMime: info.mime,
            uploadMode: UPLOAD_MODE.OSS_TOKEN,
          },
          status: STATUS.SUCCESS,
          duration,
          fileSize,
        });
        onUploadStatusChange({
          url: getUrl,
          objectName: res.url,
          success: true,
        }, index)
        return {
          url: getUrl,
          objectName: res.url,
          fileSize: fileSizeInfo.size,
          fileSizeUnit: fileSizeInfo.unit,
          fileMime: info.mime,
        }
      }).catch(e => {
        const duration = Date.now() - startTime
        sendLogger({
          stage: STAGE.UPLOAD_END,
          outParams: {
            duration,
            fileUrl: getUrl,
          },
          status: STATUS.FAILURE,
          duration,
          error: e
        });
        onUploadStatusChange({
          success: false,
          error: e,
        }, index)
        throw e
      })
    } catch (err) {
      onUploadStatusChange({
        success: false,
        error: err,
      }, index)
      throw err
    }
  })
}

