import { FileInfo, FileSize } from "../types"
import getMimeByExt from "./getMimeByExt"


export function isFile(input) {
  return (input instanceof File);
}

export function isDataUrl(url) {
  const regex = /^\s*data:([a-zA-Z]+\/[\w\+-]+(;charset=[\w-]+)?(;[\w-]+=[\w-]+)?)?;base64,[a-zA-Z0-9+/]+={0,2}\s*$/;
  return regex.test(url);
}

/**
 * 获取图片的mime类型
 * 参数 dataUrl
 */
export function getMimeTypeFromDataUrl(dataUrl: string) {
  const dataUrlArr = dataUrl.split(',');
  const mime = dataUrlArr[0].match(/:(.*?);/)?.[1];
  return mime || '';
}

export function getFileSize(file: File | string): FileSize {
  if (isFile(file)) {
    return {
      size: (file as File).size,
      unit: 'B',
    }
  }
  return {
    size: Buffer.byteLength(file as string),
    unit: 'B',
  }
}

export function getFileMime(file: File | string): string {
  if (isFile(file)) {
    const type = (file as File).type
    if (type) {
      return type
    }
    return getMimeByExt((file as File).name)
  }

  if (isDataUrl(file)) {
    return getMimeTypeFromDataUrl(file as string)
  }

  return 'text/plain'
}


export function getFileData(file: File | string): File | Buffer {
  if (isFile(file)) {
    return file as File
  }

  if (isDataUrl(file)) {
    const base64 = (file as string).split(',')[1]
    return Buffer.from(base64, 'base64')
  }

  return Buffer.from(file as string)
}

export function getFileInfo(file: File | string): FileInfo {
  return {
    mime: getFileMime(file),
    data: getFileData(file),
    size: getFileSize(file),
  }
}

export function getBase64ByFile(file: File) {
  return new Promise<string>((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = error => reject(error);
  })
}