import Mtop from '@ali/rxpi-mtop';
import { API_GATEWAY, API_VERSION } from '../constants';
import { CommonRequestConfig } from '../../interface/mtop';

type DeleteUrlConfig = {
  prefix?: string;
  subDomain?: 'm' | 'wapa' | 'waptest';
  objectName: string[];
};

type DeleteUrlRes = {
  data: {
    context: {
      debugTraces: {
        backup: string;
        backupKey: string;
      };
      eagleEyeId: string;
      server: string;
      systemTime: number;
    };
    success: boolean;
    msg?: string;
  };
};

export default function deleteUrls(config: DeleteUrlConfig): Promise<DeleteUrlRes> {
  const {
    prefix = 'temp',
    objectName,
    subDomain,
  } = config;

  const requestConfig: CommonRequestConfig = {
    api: API_GATEWAY,
    v: API_VERSION,
    type: 'POST',
    method: 'POST', // 兼容支付宝小程序
    data: {
      fcId: '240417431173407785', // 函数id
      fcGroup: 'fl-images', // 函数组
      fcName: 'deleteUrl', // 函数名
      fcData: JSON.stringify({
        prefix,
        objectName,
      }), // Faas参数
      fcConfig: JSON.stringify({
        timeout: 60000,
        disasterRecover: true,
      }), // 网关配置参数
    },
  };
  if (subDomain) {
    requestConfig.subDomain = subDomain;
  }

  return new Promise((resolve, reject) => {
    Mtop.request(
      requestConfig,
      (res) => {
        resolve(res);
      },
      (error: Error) => {
        reject(error);
      },
      undefined,
    );
  });
}
