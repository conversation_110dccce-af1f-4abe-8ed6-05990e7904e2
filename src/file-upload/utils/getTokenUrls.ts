import Mtop from '@ali/rxpi-mtop';
import { API_GATEWAY, API_VERSION } from '../constants';
import { CommonRequestConfig } from '../../interface/mtop';

type GetTokenUrlConfig = {
  prefix?: string;
  expires?: number;
  subDomain?: 'm' | 'wapa' | 'waptest';
  mimes: string[];
  requestType?: 'url' | 'token';
  uploadNumber?: number;
};

export type UrlsItem = {
  get: string; // 回显图片使用的地址
  put: string; // 上传图片使用的地址
  mine: string;
  putHeaders: {
    [key: string]: any;
  };
}
/**
 * "data": {
        "context": {
            "debugTraces": {
                "backup": "true",
                "backupKey": "pre_function_data_fl-images_uploadByTokenUrl_6d31e549ad2e064211918261e975b7d6"
            },
            "eagleEyeId": "2147bef717091221343736946ebef8",
            "server": "serverless-gateway033003209254.pre.na610|33.3.209.254",
            "systemTime": 1709122134849
        },
        "result": {
            "status": "success",
            "requestType": "token",
            "credentials": {
                "accessKeyId": "STS.NUqkp2EJFQD5vC29sryXMcaCh",
                "accessKeySecret": "8dnqerRB3yu5ZF8mqCQcqWsumh6iMA9b97hg33Pk5vdc",
                "expiration": "2024-02-28T12:23:54Z",
                "securityToken": "CAIS4gJ1q6Ft5B2yfSjIr5bEIMqGqJVn5obedGWD3XMndddhjKToijz2IHhMenFuBOkbtvk+nG5T6/oclqQqFMcUHBaeNJAvvsQGqa90pB934p7b16cNrbH4M7P6aXeirgq7AYjQSNfaZY3zCTTtnTNyxr3XbCirW0ffX7SClZ9gaKZhPGy/diEUPMpKAQFgpcQGVx7WLu3/HRP2pWDSAUF0wA4e71ly8qOi2MaRxwPDhVnhsI8vqp/2P4KvYrtUXrF2WMzn2/dtJK3ay2tb7V1R9K5wg6xN+GWF7NCYClZcphydKOfI6Zh3Ng5nb7Q3XLNcqP/1mbpkt+nL0Ln+7Adfbc98Aj75Xo2/hsncFeyLTo9pLe6kYSyRio/Xasas7F4eDChFZF8QSb0IMWRtDBEgcDbeJ5K89UrCCgXZEPDdjvlojMMolwi5pYTRegTRWdGX1ScdM5AxdFkvMxMGMtxHuynZgmcagAGihoV/BdPInQI3gHrJnGq0LGaLYG0WaL3yk8nZnEQE6hT2GWCDx/4sVa4DZnkoMV2Y6XegDRUKblnGYhbdMJIxLZnjvhOpO5i6l6c+xOt+gsSK6QaBDBB0Y/jCuN22mdtoaeYSyhlNHWyK2ApJksOgPL5ZJYVkasO+0ICgDPiRPyAA"
            },
            "willUploadResPathNamesMaps": [
                {
                    "putHeaders": {
                        "Content-Type": "image/jpeg",
                        "x-oss-meta-userid": 2215107621208,
                        "x-oss-forbid-overwrite": true
                    },
                    "willUploadResPathName": "upload/temp/UaOyz8FA9rNrew.jpeg",
                    "mime": "image/jpeg"
                }
            ],
            "clientInfo": {
                "bucket": "image-assets-private",
                "region": "oss-cn-shanghai",
                "endpoint": "https://images1.fliggy.com",
                "cname": true
            }
        },
        "success": true
    },
 */

type TokenResult = {
  status?: string;
  msg?: string;
  requestType: 'url' | 'token';
  credentials?: {
    accessKeyId: string;
    accessKeySecret: string;
    expiration: string;
    securityToken: string;
  };
  willUploadResPathNamesMaps?: {
    putHeaders: {
      [key: string]: any;
    };
    willUploadResPathName: string;
    mime: string;
  }[];
  clientInfo?: {
    bucket: string;
    region: string;
    endpoint: string;
    cname: boolean;
  };
}

type UrlResult = {
  status?: string;
  msg?: string;
  requestType: 'url' | 'token';
  urls: UrlsItem[];
}

type GetTokenUrlRes = {
  data: {
    context: {
      debugTraces: {
        backup: string;
        backupKey: string;
      };
      eagleEyeId: string;
      server: string;
      systemTime: number;
    };
    result: TokenResult | UrlResult;
    success: boolean;
    msg?: string;
  };
};

export default function getTokenUrls(config: GetTokenUrlConfig): Promise<GetTokenUrlRes> {
  const {
    prefix = 'temp',
    expires,
    subDomain,
    mimes,
    requestType = 'url',
    uploadNumber = 1,
  } = config;

  const requestConfig: CommonRequestConfig = {
    api: API_GATEWAY,
    v: API_VERSION,
    type: 'POST',
    method: 'POST', // 兼容支付宝小程序
    data: {
      fcId: '240417431173407785', // 函数id
      fcGroup: 'fl-images', // 函数组
      fcName: 'uploadByTokenUrl', // 函数名
      fcData: JSON.stringify({
        requestType,
        mimes,
        prefix,
        expires,
        uploadNumber,
      }), // Faas参数
      fcConfig: JSON.stringify({
        timeout: 60000,
        disasterRecover: true,
      }), // 网关配置参数
    },
  };
  if (subDomain) {
    requestConfig.subDomain = subDomain;
  }

  return new Promise((resolve, reject) => {
    Mtop.request(
      requestConfig,
      (res) => {
        resolve(res);
      },
      (error: Error) => {
        reject(error);
      },
      undefined,
    );
  });
}
