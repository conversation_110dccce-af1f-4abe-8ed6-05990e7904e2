export const MIME_TYPES_MAP = {
  'txt': 'text/plain',
  'html': 'text/html',
  'htm': 'text/html',
  'css': 'text/css',
  'js': 'application/javascript',
  'jsx': 'text/jsx',
  'json': 'application/json',
  'xml': 'text/xml',
  'csv': 'text/csv',
  'png': 'image/png',
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'gif': 'image/gif',
  'bmp': 'image/bmp',
  'tif': 'image/tiff',
  'tiff': 'image/tiff',
  'ico': 'image/x-icon',
  'svg': 'image/svg+xml',
  'svgz': 'image/svg+xml',
  'webp': 'image/webp',
  'pdf': 'application/pdf',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'xls': 'application/vnd.ms-excel',
  'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'ppt': 'application/vnd.ms-powerpoint',
  'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'odt': 'application/vnd.oasis.opendocument.text',
  'ods': 'application/vnd.oasis.opendocument.spreadsheet',
  'odp': 'application/vnd.oasis.opendocument.presentation',
  'mp3': 'audio/mpeg',
  'mp4': 'video/mp4',
  'ogg': 'audio/ogg',
  'ogv': 'video/ogg',
  'webm': 'video/webm',
  'wav': 'audio/wav',
  'flac': 'audio/flac',
  'avi': 'video/x-msvideo',
  'mov': 'video/quicktime',
  'mkv': 'video/x-matroska',
  'zip': 'application/zip',
  'rar': 'application/x-rar-compressed',
  '7z': 'application/x-7z-compressed',
  'tar': 'application/x-tar',
  'gz': 'application/gzip',
  'bz2': 'application/x-bzip2',
  // 添加更多的 MIME 类型映射...
};

export default function getMimeByExt(filename: string, defaultExt = 'txt') {
  const ext = filename?.split('.').pop()?.toLowerCase() || defaultExt;
  return MIME_TYPES_MAP[ext];
}
