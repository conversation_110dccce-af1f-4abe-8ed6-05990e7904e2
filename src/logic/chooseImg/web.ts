/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import Bridge from '@ali/alitrip-bridge';
import { isAlitrip, isAlipay, isWindVane } from '@ali/alitrip-env';
import { isAndroid, isUniApp, isWeb, isDiDiH5 } from '@ali/rxpi-env';
import { getBridge } from '@ali/rxpi-utils';

import { ChooseImgFuncProps } from '../../interface';
import { ERROR_TYPE } from '../../constants';
import getMimeByExt from '../../file-upload/utils/getMimeByExt';

const Photo = (
  isWeb ? require('@ali/uniapi-photo') : {}
) as typeof import('@ali/uniapi-photo');
const { canIUse } = (
  isWeb ? require('@ali/uniapi-ability') : {}
) as typeof import('@ali/uniapi-ability');

/**
 * 通过飞猪APP提供的桥方法拿图片
 */
export const alitripTakePhoto = ({
  maxSelect,
  camera,
  // 飞猪容器不支持直接唤起相册
  // album
}: ChooseImgFuncProps): Promise<string[]> =>
  new Promise((resolve, reject) => {
    console.log('chooseImage alitripTakePhoto called');
    // 处理取消逻辑
    let isCancel = true;
    // webview显示时回调
    function onVisible() {
      setTimeout(() => {
        // 一秒后检查
        if (isCancel) {
          removeListener();
          reject(ERROR_TYPE.CHOOSE_CANCEL);
        }
      }, 1000);
    }

    // 清空取消事件监听
    function removeListener() {
      document.removeEventListener('WV.Resume', onVisible);
    }
    document.addEventListener('WV.Resume', onVisible, { once: true });

    Bridge.call(
      'take_photo',
      {
        uploadType: 3,
        maxSelect,
        pickType: camera ? 1 : 0,
      },
      (ret) => {
        console.log('chooseImage alitripTakePhoto ret', ret);
        // 取消事件相关
        isCancel = false;
        removeListener();
        const { imgUrls = false } = ret;
        if (imgUrls && imgUrls[0]) {
          resolve(imgUrls);
        } else {
          reject(ERROR_TYPE.CHOOSE_FAIL);
        }
      },
      (e) => {
        console.log('chooseImage alitripTakePhoto error', e);
        reject(ERROR_TYPE.CHOOSE_FAIL);
      },
    );
  });

/**
 * 通过支付宝提供的桥方法拿图片
 */
export const alipayTakePhoto = ({
  maxSelect,
  camera,
  album,
}: ChooseImgFuncProps): Promise<string[]> =>
  new Promise((resolve, reject) => {
    console.log('chooseImage alipayTakePhoto called');
    window.AlipayJSBridge.call(
      'chooseImage',
      {
        sourceType: album
          ? ['album']
          : camera
            ? ['camera']
            : ['camera', 'album'],
        count: maxSelect,
      },
      (result) => {
        if (result.errorCode || result.error) {
          reject(ERROR_TYPE.CHOOSE_CANCEL);
          return;
        }
        let apFilePath = result.apFilePathsV2 || result.apFilePaths || [];
        if (typeof apFilePath === 'string') {
          try {
            apFilePath = JSON.parse(apFilePath);
          } catch (e) {
            apFilePath = apFilePath || '';
          }
        }
        if (!apFilePath.length || !/^https?:/.test(apFilePath[0])) {
          return;
        }
        if (apFilePath && apFilePath[0]) {
          resolve(apFilePath);
        } else {
          reject(ERROR_TYPE.CHOOSE_FAIL);
        }
      },
      () => {
        reject(ERROR_TYPE.CHOOSE_FAIL);
      },
    );
  });

/**
 * 通过手淘提供的uni-api方法拿图片
 */
export const uniAppTakePhoto = ({
  maxSelect,
  camera,
  album,
  taobaoBizName,
}): Promise<string[]> =>
  new Promise((resolve, reject) => {
    console.log('chooseImage uniAppTakePhoto called');
    // 适配鸿蒙 refer to https://aliyuque.antfin.com/beoubq/yr8idb/slzcg38mltfn7wfc#rDUBJ
    if (!album && camera) {
      // album 优先级高，album 不为true 且 camera 为true时拍照
      Photo.takeFromCamera({
        type: Photo.MediaType.photo,
        maxSelection: maxSelect,
        bizName: taobaoBizName,
        // imgUploadBizCode: 'dailytest',
        // needUpload: true,
        needThumbBase64: Photo.ToggleState.true,
        onSuccess: (event) => {
          // console.log('chooseImage uni app takeFromCamera success :', event)
          if (event?.mediaFiles?.length > 0) {
            resolve(
              event?.mediaFiles?.map((ele) => {
                const mime = getMimeByExt(ele.localPath);
                return `data:${mime};base64,${ele.thumbBase64}`;
              }),
            );
          } else {
            reject(ERROR_TYPE.CHOOSE_FAIL);
          }
        },
      });
    } else {
      Photo.takeFromPhotoLibrary({
        type: Photo.MediaType.photo,
        bizName: taobaoBizName,
        // imgUploadBizCode: 'dailytest',
        // needUpload: true,
        needThumbBase64: Photo.ToggleState.true,
        maxSelection: maxSelect,
        extra: {},
        onSuccess: (event) => {
          // console.log('takeFromPhotoLibrary uni app success: ', event)
          if (event?.mediaFiles?.length > 0) {
            resolve(
              event?.mediaFiles?.map((ele) => {
                const mime = getMimeByExt(ele.localPath);
                return `data:${mime};base64,${ele.thumbBase64}`;
              }),
            );
          } else {
            reject(ERROR_TYPE.CHOOSE_FAIL);
          }
        },
      });
    }
  });

/**
 * 通过手淘提供的桥方法拿图片
 */
export const windVaneTakePhoto = ({
  maxSelect,
  camera,
  album,
}): Promise<string[]> =>
  new Promise((resolve, reject) => {
    console.log('chooseImage windVaneTakePhoto called');
    const mutipleSelection = '0'; // WindVane 图库多选能力在Android 13及以上存在问题，这里限制为单选！！refer to https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPPNL2rXIE5KE35LJzN67Mw4?corpId=dingd8e1123006514592&utm_medium=im_card&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&dontjump=true&utm_scene=team_space&utm_source=im&cid=52736710746

    window.WindVane.call(
      'WVCamera',
      'takePhoto',
      {
        type: '0',
        v: '2.0',
        needBase64: true,
        mutipleSelection,
        maxSelect,
        mode: album ? 'photo' : camera ? 'camera' : 'both',
        reducePermission: true, // 符合工信部的权限要求
      },
      (res) => {
        if (res.status !== 'SUCCESS' && res.ret !== 'HY_SUCCESS') {
          reject(ERROR_TYPE.CHOOSE_CANCEL);
        }
        const urlList = res.url ? [res.url] : res?.images.map((ele) => ele.url);

        if (urlList.length) {
          resolve(urlList);
        } else {
          reject(ERROR_TYPE.CHOOSE_FAIL);
        }
      },
      () => {
        reject(ERROR_TYPE.CHOOSE_FAIL);
      },
    );
  });

export const diDiTakePhoto = ({ camera }): Promise<string[]> =>
  new Promise(async (resolve, reject) => {
    const photograph = getBridge()?.photograph
    if (!photograph) {
      reject(ERROR_TYPE.CHOOSE_FAIL);
      return;
    }
    photograph(
      {
        type: camera ? 'photograph' : 'album', // photograph（相机）， album（相册）， choice（菜单
      },
      function (res) {
        const { photograph_result, image, type } = res;
        let mime;
        switch (photograph_result) {
          case -1:
            reject(ERROR_TYPE.CHOOSE_FAIL);
            break;
          case 0:
            mime = getMimeByExt(type);
            console.log('chooseImage diDiTakePhoto mime', mime);
            resolve([`data:${mime};base64,${image}`]);
            break;
          case 1:
            reject(ERROR_TYPE.CHOOSE_FAIL);
            break;
          case 2:
            reject(ERROR_TYPE.CHOOSE_CANCEL);
            break;
        }
      },
    );
  });
/**
 * 通过原生input拿图片
 */
export const inputTakePhoto = (
  { maxSelect, enablePDF, camera }: ChooseImgFuncProps,
  needBase64 = true,
)/*: Promise< File[] |string[] >*/ =>  // 返回 File[] |string[]类型，但为了兼容小程序，这里注释掉，小程序没有 File
  new Promise((resolve, reject) => {
    console.log('chooseImage inputTakePhoto called');
    const touchTapTime = new Date().valueOf();
    const fileInputElement = document.createElement(
      'INPUT',
    ) as HTMLInputElement;
    if (maxSelect > 1) {
      fileInputElement.multiple = true;
    }
    fileInputElement.type = 'file';
    camera && (fileInputElement.capture = 'camera');
    // 飞猪安卓 要支持pdf的话 如果这么写，会不支持上传图片
    // fileInputElement.accept = 'application/pdf, image/*';
    // 飞猪ios 要支持pdf的话，如果这么写，会不支持上传pdf
    // fileInputElement.accept = 'application/*, image/*';

    // 所以最后我们都判断一下
    if (isAndroid) {
      fileInputElement.accept = `${enablePDF ? 'application/*' : ''}, image/*`;
    } else {
      fileInputElement.accept = `${
        enablePDF ? 'application/pdf' : ''
      }, image/*`;
    }

    //  处理取消逻辑
    let isCancel = true;

    function onWindowFocus() {
      // 如果事件距离触发时间小于 50ms 我们认为是原生事件触发，非用户触发 直接return 主要解决 onTouchTap 直接触发touchEnd 的问题
      if (new Date().valueOf() - touchTapTime < 50) {
        console.log('由原生onTouchTap 事件触发');
        return;
      }
      setTimeout(() => {
        if (isCancel) {
          removeListener();
          reject(ERROR_TYPE.CHOOSE_CANCEL);
        }
      }, 1000);
    }

    // 清空取消事件监听
    function removeListener() {
      window.removeEventListener('touchend', onWindowFocus);
      window.removeEventListener('focus', onWindowFocus);
      document.removeEventListener('WV.Resume', onWindowFocus);
    }
    //  由于部分浏览器上 touchTap 会直接触发 touchEnd 事件，所以 我们这里不加once参数
    window.addEventListener('touchend', onWindowFocus);
    window.addEventListener('focus', onWindowFocus, { once: true });
    document.addEventListener('WV.Resume', onWindowFocus, { once: true });

    fileInputElement.addEventListener('change', function () {
      // 取消相关逻辑
      isCancel = false;
      removeListener();

      // 获取文件流
      const fileList = Array.from(this.files);
      const urlList: string[] = [];

      fileList.forEach((file) => {
        if (
          !/image\/\w+/.test(file.type) &&
          !/application\/pdf/.test(file.type)
        ) {
          reject(ERROR_TYPE.CHOOSE_FAIL);
        }
        if (needBase64) {
          const reader = new FileReader();
          reader.onload = function (t) {
            const result = t?.target?.result as string;
            urlList.push(result);
            if (urlList.length === fileList.length) {
              resolve(urlList);
            }
          };
          reader.readAsDataURL(file);
        } else {
          resolve(fileList);
        }
      });
    });
    // 需要插入一个不渲染的的dom
    // @ts-ignore Cannot assign to style because it is a read-only property
    fileInputElement.style = 'display:none';
    // 或者可以使用这个
    // fileInputElement.setAttribute('style', 'display:none');
    document.body.appendChild(fileInputElement);
    fileInputElement.click();
  });

/**
 * 高德桥方法一次只能选一张
 * 体验不如降级到h5 1.调用桥方法不可多选图片 2. 卡顿，无明显的优化效果
 * 如果有一天可以支持好，那可以再启用
 */
//      依赖
//   // import AmapApp from "@ali/amap-lib";
// export const amapTakePhoto = ({
//   maxSelect,
// }: ChooseImgFuncProps): Promise<string[]> => {
//   var obj = {
//     action: "addPhoto",
//     businessName: "fliggy_image_upload",
//     titleText: "请选择图片",
//     maxLength: "2000",
//   };
//
//   return new Promise((resolve, reject) => {
//     try {
//       AmapApp.bridge.ready(function () {
//         AmapApp.bridge.send(obj, function (res) {
//           console.log(res);
//           // console.log(res.imgbase64.subString(0,30)); // 客户端的响应
//           // resolve(["data:image/jpeg;base64," + res.imgbase64]);
//         });
//       });
//     } catch (e) {
//       console.log("调用高德桥方法失败,降级h5");
//       inputTakePhoto({ maxSelect });
//     }
//   });
// };

/**
 * 选择图片main方法
 */
export default async (
  restProps,
  chooseByNative,
  needBase64 = true,
): Promise<string[]> => {
  if (isAlitrip && !chooseByNative) {
    return await alitripTakePhoto(restProps);
  } else if (isAlipay && !chooseByNative) {
    return await alipayTakePhoto(restProps);
  } else if (
    isUniApp &&
    isAndroid &&
    restProps?.taobaoBizName &&
    canIUse(Photo.takeFromPhotoLibrary) &&
    canIUse(Photo.takeFromCamera) &&
    !chooseByNative
  ) {
    // uni-api ios 存在问题，先降级为 input
    return await uniAppTakePhoto(restProps);
  } else if (isWindVane && isAndroid && !chooseByNative) {
    return await windVaneTakePhoto(restProps);
  } else if (isDiDiH5 && isAndroid) {
    // 安卓滴滴端只能通过桥方法获取图片；安卓IOS端只能通过input获取图片
    return await diDiTakePhoto(restProps);
  } else {
    return await (inputTakePhoto(restProps, needBase64) as Promise<string[]>);
  }
};
