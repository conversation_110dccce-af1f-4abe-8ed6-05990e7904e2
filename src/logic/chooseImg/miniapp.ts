/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import { chooseImage } from '@uni/image';

import { ChooseImgFuncProps } from '../../interface';
import { ERROR_TYPE } from '../../constants';

/**
 * 小程序拿图片
 */
const miniappTakePhoto = async ({
  maxSelect,
  camera,
  album
}: ChooseImgFuncProps): Promise<string[]> => {
  try {
    const result = await chooseImage({
      count: maxSelect,
      sourceType: album ? ['album'] : camera ? ['camera'] :['album', 'camera'],
    });
    if (result && result.tempFiles) {
      return result.tempFiles.map((val) => {
        return `${val.path}?currentSize=${val.size}`;
      });
    }
    // fk手淘ide和真机的字段居然还不一致，这里做一下兼容
    if (result && result.tempFilePaths) {
      return result.tempFilePaths
    }
    return [];
  } catch (err) {
    throw ERROR_TYPE.CHOOSE_FAIL;
  }
};

export default miniappTakePhoto;
