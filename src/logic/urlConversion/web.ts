/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */

import { Image2CanvasConfig } from '../../interface/compress';
import { ERROR_TYPE, MAX_IMAGE_WIDTH } from '../../constants';

/**
 * 将dataURL字符串转变为image对象
 *
 * @param {string} dataURL - dataURL字符串
 * @returns {Promise(Image)}
 */
export function dataURLtoImage(dataURL: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = function () {
      // 超出最大宽度的图片，进行等比缩小
      if (img.width >= MAX_IMAGE_WIDTH) {
        const oldWidth = img.width;
        img.width = MAX_IMAGE_WIDTH;
        img.height = (img.height / oldWidth) * MAX_IMAGE_WIDTH;
      }

      return resolve(img);
    };
    img.onerror = () => {
      reject(ERROR_TYPE.CONVERT_BASE_FAIL);
    };
    img.src = dataURL;
  });
}

/**
 * 将一个image对象转变为一个canvas对象
 *
 * @type {Image2CanvasConfig}
 * @returns {Promise(canvas)}
 */
export async function imagetoCanvas(
  image: HTMLImageElement,
  config: Image2CanvasConfig = {},
): Promise<HTMLCanvasElement> {
  const myConfig = { ...config };
  const cvs = document.createElement('canvas');
  const ctx = cvs.getContext('2d');
  let height;
  let width;
  // eslint-disable-next-line no-unused-vars
  for (const i in myConfig) {
    if (Object.prototype.hasOwnProperty.call(myConfig, i)) {
      myConfig[i] = Number(myConfig[i]);
    }
  }
  // 设置宽高
  if (!myConfig.scale) {
    width =
      myConfig.width ||
      (myConfig.height * image.width) / image.height ||
      image.width;
    height =
      myConfig.height ||
      (myConfig.width * image.height) / image.width ||
      image.height;
  } else {
    // 缩放比例0-10，不在此范围则保持原来图像大小
    const scale =
      myConfig.scale > 0 && myConfig.scale < 10 ? myConfig.scale : 1;
    width = image.width * scale;
    height = image.height * scale;
  }
  // 当顺时针或者逆时针旋转90时，需要交换canvas的宽高
  if ([5, 6, 7, 8].some((i) => i === myConfig.orientation)) {
    cvs.height = width;
    cvs.width = height;
  } else {
    cvs.height = height;
    cvs.width = width;
  }
  // 设置方向
  switch (myConfig.orientation) {
    case 3:
      ctx.rotate((180 * Math.PI) / 180);
      ctx.drawImage(image, -cvs.width, -cvs.height, cvs.width, cvs.height);
      break;
    case 6:
      ctx.rotate((90 * Math.PI) / 180);
      ctx.drawImage(image, 0, -cvs.width, cvs.height, cvs.width);
      break;
    case 8:
      ctx.rotate((270 * Math.PI) / 180);
      ctx.drawImage(image, -cvs.height, 0, cvs.height, cvs.width);
      break;
    case 2:
      ctx.translate(cvs.width, 0);
      ctx.scale(-1, 1);
      ctx.drawImage(image, 0, 0, cvs.width, cvs.height);
      break;
    case 4:
      ctx.translate(cvs.width, 0);
      ctx.scale(-1, 1);
      ctx.rotate((180 * Math.PI) / 180);
      ctx.drawImage(image, -cvs.width, -cvs.height, cvs.width, cvs.height);
      break;
    case 5:
      ctx.translate(cvs.width, 0);
      ctx.scale(-1, 1);
      ctx.rotate((90 * Math.PI) / 180);
      ctx.drawImage(image, 0, -cvs.width, cvs.height, cvs.width);
      break;
    case 7:
      ctx.translate(cvs.width, 0);
      ctx.scale(-1, 1);
      ctx.rotate((270 * Math.PI) / 180);
      ctx.drawImage(image, -cvs.height, 0, cvs.height, cvs.width);
      break;
    default:
      ctx.drawImage(image, 0, 0, cvs.width, cvs.height);
  }
  return cvs;
}

/**
 * 通过URL获取base64
 *
 * @returns {Promise(string[])}
 */
export default async function getBase64ByURL(
  urlList: string[],
  imgCanvasOptions?: Image2CanvasConfig,
): Promise<string[]> {
  const baseStringList = urlList.map(async (dataURL) => {
    if (
      dataURL.search('data:image') === 0 ||
      dataURL.search('data:application/pdf') === 0
    ) {
      return dataURL;
    }
    // try {
    const image: HTMLImageElement = await dataURLtoImage(dataURL);
    const canvas: HTMLCanvasElement = await imagetoCanvas(
      image,
      Object.assign({}, imgCanvasOptions),
    );
    const baseString = canvas.toDataURL('image/jpeg', 1);
    return baseString;
    // } catch (err) {
    //   throw err;
    // }
  });

  // 因为做了异常捕获，所以这里用all
  return Promise.all(baseStringList);
}
