/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import { isMiniApp, isWeChatMiniProgram } from 'universal-env';

import { ERROR_TYPE } from '../../constants';

declare const my: {
  getFileSystemManager: () => {
    (): any;
    readFile?: any;
    readFileSync: { (arg0: any, arg1: string): string; new (): any };
    new (): any;
  };
};

async function wxGetBase64ByURL(url): Promise<string> {
  return new Promise((resolve, reject) => {
    const fs = wx.getFileSystemManager();
    fs.readFile({
      filePath: url,
      encoding: 'base64',
      success: (res) => {
        if (res.data) {
          resolve(res.data);
        } else {
          reject(new Error('wx.getFileSystemManager().readFile 未获取到data'));
        }
      },
      fail: () => {
        reject(new Error('wx.getFileSystemManager().readFile fail'));
      },
    });
  });
}

function aliGetBase64ByURL(url): Promise<string> {
  return new Promise((resolve, reject) => {
    const fs = my.getFileSystemManager();
    fs.readFile({
      filePath: url,
      encoding: 'base64',
      success: (res) => {
        if (res.data) {
          resolve(res.data);
        } else {
          reject(new Error('my.getFileSystemManager 未获取到data'));
        }
      },
      fail: () => {
        reject(new Error('my.getFileSystemManager fail'));
      },
    });
  });
}

/**
 * 通过URL获取base64
 *
 * @returns {Promise(string[])}
 */
export default async function getBase64ByURL(
  urlList: string[],
): Promise<string[]> {
  const baseStringList = urlList.map(async (dataURL) => {
    if (dataURL.search('data:image') === 0) {
      return dataURL;
    }
    try {
      let baseString = '';
      if (isWeChatMiniProgram) {
        baseString = await wxGetBase64ByURL(dataURL);
      } else if (isMiniApp) {
        baseString = await aliGetBase64ByURL(dataURL);
      }
      return baseString;
    } catch (err) {
      console.error('mini-getBase64ByURL', err);
      throw ERROR_TYPE.CONVERT_BASE_FAIL;
    }
  });

  // 因为做了异常捕获，所以这里用all
  return Promise.all(baseStringList);
}
