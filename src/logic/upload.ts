/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import { uploadBaseUrl } from '../service';
import { guid, allSettled } from '../utils';
import {
  API_GATEWAY,
  UPLOAD_LIMIT_SIZE,
  API_VERSION,
  STATUS_TYPE,
  ERROR_TYPE,
} from '../constants';
import { CommonResponse } from '../interface/mtop';
import { UploadConfigProps, UploadParam, OnStatusChange } from '../interface';

const uploadImage = async (
  baseStringList: string[],
  uploadConfig: UploadConfigProps = {},
  onStatusChange: OnStatusChange,
) => {
  const {
    onAfterRequest,
    onBeforeRequest,
    process,
    prefix,
    expires,
    customBucket,
    subDomain,
  } = uploadConfig;
  const currentList = new Array(baseStringList.length);
  const onUploadStatusChange = (value, currentIndex) => {
    currentList[currentIndex] = value;
    onStatusChange && onStatusChange(STATUS_TYPE.UPLOAD_ING, currentList);
  };

  const promiseList = baseStringList.map((base64, index) => {
    const getRequestConfig = (restData, isFragmentation) => {
      let __requestConfig;
      if (onBeforeRequest) {
        __requestConfig = onBeforeRequest(restData, isFragmentation);
      }

      __requestConfig = __requestConfig || {
        api: API_GATEWAY,
        v: API_VERSION,
        type: 'POST',
        method: 'POST', // 兼容支付宝小程序
        data: {
          fcId: '240417431173407785', // 函数id
          fcGroup: 'fl-images', // 函数组
          fcName: isFragmentation ? 'multUploadByBase64' : 'uploadByBase64', // 函数名
          fcData: JSON.stringify({
            process,
            prefix,
            expires,
            customBucket,
            ...restData,
          }), // Faas参数
          fcConfig: JSON.stringify({
            timeout: 60000,
            disasterRecover: true,
          }), // 网关配置参数
        },
      };
      if (subDomain) {
        __requestConfig.subDomain = subDomain;
      }

      return __requestConfig;
    };

    const uploadParam: UploadParam = {
      baseString: base64,
      getRequestConfig,
      onAfterRequest: (result) => {
        let __result;
        if (typeof onAfterRequest === 'function') {
          __result = onAfterRequest(result);
        }
        if (!__result) {
          __result = result?.data?.result;
        }
        return __result;
      },
      onUploadStatusChange,
      currentIndex: index,
    };

    return base64.length < UPLOAD_LIMIT_SIZE
      ? uploadWhole(uploadParam)
      : uploadFragmentation(uploadParam);
  });

  return Promise.allSettled
    ? Promise.allSettled(promiseList)
    : allSettled(promiseList);
};

// 上传分片
const uploadFragmentation = async ({
  baseString,
  getRequestConfig,
  onAfterRequest,
  onUploadStatusChange,
  currentIndex,
}: UploadParam) => {
  try {
    const uniqueTag = guid();
    const loopCount = Math.ceil(baseString.length / UPLOAD_LIMIT_SIZE);
    const fragmentList = [];
    // 切割base64字符串
    for (let i = 0; i < loopCount; i++) {
      if (i === loopCount - 1) {
        fragmentList.push(
          baseString.substring(i * UPLOAD_LIMIT_SIZE, baseString.length),
        );
      } else {
        fragmentList.push(
          baseString.substring(
            i * UPLOAD_LIMIT_SIZE,
            (i + 1) * UPLOAD_LIMIT_SIZE,
          ),
        );
      }
    }
    // 创建上传队列
    const pipelineList = fragmentList.map(async (val, index) =>
      uploadBaseUrl(getRequestConfig({ index, uniqueTag, base64: val }, true)),
    );

    try {
      //  上传完成之后，通过传uuid拿到最终oss里的URL
      const resultList = await Promise.all(pipelineList);
      if (resultList) {
        const result = await uploadBaseUrl<CommonResponse>(
          getRequestConfig({ uniqueTag }, true),
        );
        if (result) {
          const { url, objectName } = onAfterRequest(result);

          if (url) {
            const newResult = objectName ? { objectName, url } : { url };
            onUploadStatusChange &&
              onUploadStatusChange(newResult, currentIndex);
            return newResult;
          } else {
            throw new Error(JSON.stringify(result));
          }
        }
        throw ERROR_TYPE.UPLOAD_FAIL;
      }
      throw ERROR_TYPE.UPLOAD_FAIL;
    } catch (err) {
      throw ERROR_TYPE.UPLOAD_FAIL;
    }
  } catch (err) {
    throw ERROR_TYPE.UPLOAD_FAIL;
  }
};

// 正常上传不分片
const uploadWhole = async ({
  baseString,
  getRequestConfig,
  onAfterRequest,
  onUploadStatusChange,
  currentIndex,
}: UploadParam) => {
  try {
    if (!baseString) {
      throw ERROR_TYPE.BASE_NULL;
    }
    const result = await uploadBaseUrl<CommonResponse>(
      getRequestConfig({ base64: baseString }),
    );
    if (result) {
      const { url, objectName } = onAfterRequest(result);

      if (url) {
        const newResult = objectName ? { objectName, url } : { url };
        onUploadStatusChange && onUploadStatusChange(newResult, currentIndex);
        return newResult;
      } else {
        throw new Error(JSON.stringify(result));
      }
    }
    throw ERROR_TYPE.UPLOAD_FAIL;
  } catch (err) {
    throw ERROR_TYPE.UPLOAD_FAIL;
  }
};

export default uploadImage;
