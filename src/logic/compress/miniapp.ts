/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import { compressImage } from '@uni/image';

import { ERROR_TYPE, COMPRESS_LIMIT_SIZE } from '../../constants';

/**
 * 根据体积压缩File（string）对象
 */
export async function compress(
  dataURL: string,
  quality?: number,
): Promise<string> {
  const result = await compressImage({ src: dataURL, quality: quality * 3 });
  if (result && result.tempFilePath) {
    // 压缩返回的URL不带后缀
    return `${result.tempFilePath}`;
  }
  throw ERROR_TYPE.COMPRESS_FAIL;
}

/**
 * 循环压缩
 */
export default async (urlList, quality, autoCompress) => {
  const resultList = urlList.map(async (val) => {
    const url = val.split('?currentSize=')[0];
    const size = Number(val.split('?currentSize=')[1] || 0);
    // 压缩策略优化, 小于600k的png图片，不启用压缩
    if (
      autoCompress &&
      !quality &&
      url.search('.png') !== -1 &&
      size < COMPRESS_LIMIT_SIZE * 1024
    ) {
      return url;
    }

    return await compress(url, quality);
  });
  return await Promise.all(resultList);
};
