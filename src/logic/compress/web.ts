/**
 * Created by 江渺<<EMAIL>> on 2021-12-13.
 * 飞猪前端上传图片组件
 */
import { Image2CanvasConfig } from '../../interface/compress';
import { dataURLtoImage, imagetoCanvas } from '../urlConversion/web';
import { COMPRESS_LIMIT_SIZE } from '../../constants';

/**
 * 根据体积压缩File（string）对象
 */
export async function compress(
  dataURL: string,
  imgCanvasOptions?: Image2CanvasConfig,
  quality?: number,
): Promise<string> {
  const image: HTMLImageElement = await dataURLtoImage(dataURL);
  const canvas: HTMLCanvasElement = await imagetoCanvas(
    image,
    Object.assign({}, imgCanvasOptions),
  );
  const originUrl: string = canvas.toDataURL('image/jpeg', quality || 1);
  // 如果传入了quality，代表不需要自动压缩，直接返回即可
  if (quality) {
    return originUrl;
  }

  const resultSize = {
    max: COMPRESS_LIMIT_SIZE * 1024,
    min: COMPRESS_LIMIT_SIZE * 0.9 * 1024,
  };
  let imageQuality = 0.5;
  let compressDataURL: string;
  let tempDataURLs = '';

  // 如果指定体积大于原文件体积，则不做处理；
  if (COMPRESS_LIMIT_SIZE * 1024 > originUrl.length) {
    return originUrl;
  }
  /**
   * HTMLCanvasElement.toBlob()以及HTMLCanvasElement.toDataURL()压缩参数
   * 的最小细粒度为0.01，而2的7次方为128，理论上来说即只要循环7次，则会覆盖所有可能性
   */
  console.time('压缩时长');
  for (let x = 1; x <= 7; x++) {
    compressDataURL = canvas.toDataURL('image/jpeg', imageQuality);
    const CalculationSize = compressDataURL.length;
    // 如果到循环第七次还没有达到精确度的值，那说明该图片不能达到到此精确度要求
    // 这时候最后一次循环出来的dataURL可能不是最精确的，需要取所有dataURL比较来选出最小的；
    if (x === 7) {
      compressDataURL = tempDataURLs || compressDataURL;
      console.log('压缩次数:', x);
      break;
    }
    if (resultSize.max < CalculationSize) {
      imageQuality -= 0.5 ** (x + 1);
    } else if (resultSize.min > CalculationSize) {
      imageQuality += 0.5 ** (x + 1);
    } else {
      console.log('压缩次数:', x);
      break;
    }

    if (
      CalculationSize > tempDataURLs.length &&
      CalculationSize < resultSize.max
    ) {
      tempDataURLs = compressDataURL;
    }
  }
  console.timeEnd('压缩时长');
  return compressDataURL;
}

export default async (urlList, { quality, imgCanvasOptions, autoCompress }) => {
  const resultList = urlList.map(async (val) => {
    // 自动压缩策略优化, 低于600k的png图片，不启用压缩
    if (
      autoCompress &&
      !quality &&
      val.search('data:image/png;base64') === 0 &&
      val.length < COMPRESS_LIMIT_SIZE * 1024
    ) {
      return val;
    }
    // 如果是pdf 跳过压缩
    if (val.search('data:application/pdf') === 0) {
      return val;
    }

    return await compress(val, imgCanvasOptions, quality);
  });
  return await Promise.all(resultList);
};
