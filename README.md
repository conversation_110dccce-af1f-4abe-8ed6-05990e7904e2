# <img src="https://img.alicdn.com/tfs/TB1JpgJRFXXXXc7XpXXXXXXXXXX-800-800.png" width="60"/> @ali/rxpi-fliggy-image-upload

![@ali/rxpi-fliggy-image-upload@ALINPM](http://web.npm.alibaba-inc.com/badge/v/@ali/rxpi-fliggy-image-upload.svg?style=flat-square) ![@ali/rxpi-fliggy-image-upload@ALINPM](http://web.npm.alibaba-inc.com/badge/d/@ali/rxpi-fliggy-image-upload.svg?style=flat-square)

> 飞猪前端上传图片组件

## demo

<https://edith.wapa.taobao.com/proxy/basement/rxpi/rxpi-fliggy-image-upload/demo/index.html>

<img src="https://gw.alicdn.com/imgextra/i3/O1CN01WvIXYv1vLV7bPHsam_!!6000000006156-2-tps-600-600.png" alt="二维码" width="200" height="200">

## 作者

- 名称：江渺，悦加
- 邮箱：<<EMAIL>>, <<EMAIL>>

## 安装

```
tnpm install @ali/rxpi-fliggy-image-upload --save
```

## 使用

#### 注意！由于取消监听事件和 onTouchTap 冲突，所以建议用 onClick 调用

#### 注意！在原生的安卓浏览器（微信h5容器）中，catch中监听的 取消上传 CHOOSE_CANCEL 事件可能存在问题，由于无法获取到用户取消上传的时机，建议业务不向用户toast取消上传的提示，主要表现为用户取消上传图片后，需要手动再点击一下页面的任意位置才会触发取消上传的错误

```js
// 下面仅仅是示例代码，实际的Usage请自行编写
import fliggyImageUpload from "@ali/rxpi-fliggy-image-upload";

const result = await fliggyImageUpload({
  maxSelect: 3,
  uploadConfig: {
    mode: "OSS_URL",
    prefix: '对应的prefix',
  }
});
```

#### 报错类型

除了上传失败的报错，其他报错都会以异常的形式抛出，由于单个图片上传失败的并不会影响其他图片的上传，所以上传报错会在结果的reason字段返回。

`选择图片超过最大张数 LIMIT_EXCEEDED`
`取消图片上传 CHOOSE_CANCEL`
`选择图片失败 CHOOSE_FAIL`
`上传失败 UPLOAD_FAIL`
`压缩失败 COMPRESS_FAIL`
`转换base64失败 CONVERT_BASE_FAIL`

## 参数概览

| 参数             | Type                                                     | Required | Default |stage| Description                                                                    |
| :--------------- | :---------------------------------------------           | :------- | :------ |:---  | :----------------------------------------------------------------------------- |
| camera           | `boolean`                                                | `false`  | `false` |图片获取| 是否走拍照模式，默认是false，如果走拍照，会忽视掉maxSelect及album参数。在IOS支付宝及Android端淘宝支付宝，album 同时为true, 走相册，album 优先级高。 |
| album           | `boolean`                                                | `false`  | `false` |图片获取| 是否走相册模式，默认是false; 在飞猪端，如果 camera 同时为true, 走拍照，camera 优先级高。|
| enablePDF           | `boolean`                                                | `false`  | `false` |图片获取| 是否支持上传PDF， 为 true 时，会自动降级为h5 input获取文件 |
| taobaoBizName        | `string`                                                | `false`  | ''   |图片获取| 在安卓淘宝app中使用uni-api获取图片需传入的bizName, bizName需要上游业务单独申请并传入，没有bizName 无法通过uni-api获取图片，会降级为h5-input元素获取。|
| maxSelect        | `number`                                                 | `false`  | `1`     |图片获取| 最多选择图片数，等于 1 的时候为单选，大于 1 为多选, 原生 h5 只支持设置单选多选；android 淘宝端使用桥方法相册只支持单选 |
| chooseByNative   | `boolean`                                                | `false`  | `false` |图片获取| 选择图片是否走 h5 input，低版本飞猪可以开启此选项                                    |
| openVerify       | `boolean`                                                | `false`  | `false` |图片获取| 是否开启校验功能，目前支持校验最大图片选择数量                                 |
| skipImageCompress| `boolean`                                                | `false`  |         |图片处理|   是否跳过图片压缩环节，如果跳过，压缩相关props: autoCompress, quality 将无效; 缺省或 false 不跳过压缩 |
| autoCompress     | `boolean`                                                | `false`  | `true`  |图片处理| 是否自动压缩，默认开启，如果不是特殊场景，建议默认开启； 将超过600k的图片压缩到600k以下；不压缩pdf； 如果传入有效的quality, 则不会进行自动压缩，quality 优先级高； |
| ~~imgCanvasOptions~~ （已弃用）| `Image2CanvasConfig`                                     | `false`  | `{}`    |图片处理| 图片转 canvas 参数，支持设置宽高，缩放比例，方向，只支持 h5; 只有在skipImageCompress为false且 autoCompress 为 false 时生效 |
| quality          | `number`                                                 | `false`  |         |图片处理| 图片压缩质量，不传则采用默认压缩算法;只有在skipImageCompress为false且时才有效 |
| uploadConfig     | `UploadConfigProps`                                      | `false`  | `true`  |图片上传| 上传参数                                                                       |
| onStatusChange   | `(type: STATUS_TYPE, statusDetail:StatusDetail[]) => void;` | `false`  |         |各阶段| 上传前各阶段钩子函数, 会返回图片本地虚拟 URL，或者 base64 字符串，或者图片链接 |

## 图片获取相关参数详情

### camera、album、chooseByNative三个属性取不同值时在各端的实际表现
参考原文：https://aliyuque.antfin.com/fliggy-fed-feedback/privacy_pictures/szcgxhnstpnrehmy?singleDoc#
不同操作系统和浏览器之间在实现这一功能时有一些差异。
我们测试了部分手机收集到的实际表现如下表：

<img src="https://gw.alicdn.com/imgextra/i2/O1CN01SBBvTZ1nHp1wCs1Ct_!!6000000005065-0-tps-2390-936.jpg" alt="table" width="600">

绿色背景单元为支持桥方法的场景

表中的菜单为 含照片、拍照、选取文件三个选项的菜单,
<img src="https://gw.alicdn.com/imgextra/i4/O1CN01hRVkLy1LZrO100UZ5_!!6000000001314-0-tps-780-343.jpg" alt="table" width="200">

IOS h5 input 获取图片的交互比较一致，camera: true 走拍照，否则走菜单。
飞猪、淘宝、支付宝使用桥方法获取图片是各自有自己的定制交互效果。
Android 端更加灵活，几乎各自有各自的交互效果。

### camera

是否走拍照模式，默认是false，如果走拍照，会忽视掉maxSelect及album参数。
如果设备不支持拍照，不会生效。

在IOS支付宝及Android端淘宝、支付宝 且 chooseByNative 为false时，album 同时为true, 走相册，album 优先级高;
其他情况，大概率，camera 优先级高。

### album

是否走相册模式，默认是false;
在IOS支付宝及Android端淘宝支付宝 且 chooseByNative 为false时，album 同时为true, 走相册，album 优先级高;
其他情况，如果 camera 同时为true, 大概率走拍照， camera 优先级高。

### taobaoBizName

在安卓淘宝app中使用uni-api获取图片需传入的bizName, bizName需要上游业务单独申请并传入，没有bizName 无法通过uni-api获取图片，会降级为h5-input元素获取。

申请bizName [参考文档](https://aliyuque.antfin.com/fliggy-fed-feedback/privacy_pictures/yevnrz183qom6w8i?singleDoc#)

### chooseByNative

只有飞猪、支付宝、Android端淘宝及Android端滴滴端支持桥方法，默认使用桥方法获取图片，可以使用chooseByNative：true 降级使用 h5 input方法获取图片。滴滴Android端不支持h5 input方法，传入chooseByNative：true 无效。
其他情况只支持使用h5 input方法获取图片,无需设置。

| 系统\端       | h5及其他 | 飞猪       | 淘宝      | 支付宝    |滴滴      |
| :----------- | :------ | :-------- | :-------- |:------  |:------  |
| Android      |  input  | 桥，input  | 桥，input |桥，input |桥 |
| IOS          |  input  | 桥，input  |    input |桥，input  |input |

使用h5 input方法获取图片在移动操作系统中中交互存在细微差异，实际表现以实测为准。
#### iOS (iPhone/iPad):
Safari浏览器支持使用 `<input type="file">` 元素来让用户选择照片或视频。
当用户点击这个元素时，会弹出一个菜单让用户选择是拍摄新照片/视频还是从相册选择。
iOS的安全限制比较严格，个别网页特性（如直接访问用户媒体库）可能受限。
第三方浏览器（如Chrome, Firefox）在iOS上受到一定限制，它们通常必须使用WebKit作为底层引擎，并且具有与Safari类似的文件获取体验。
#### Android:
相比之下，Android系统提供了更多自定义和灵活性。
不同浏览器（Chrome, Firefox, Opera等）可以有不同的实现方式。
用户点击 `<input type="file">` 时，通常会弹出一个选择器来选择拍照、录像或从文件管理器（包含相册）选择。
第三方文件管理器应用有时候也会被包含在选择器中，提供额外的文件选择选项。


### enablePDF

是否支持上传PDF， 为 true 时，会自动降级为h5 input获取文件

### maxSelect

最多选择图片数，等于 1 的时候为单选，大于 1 为多选, 原生 h5 只支持设置单选多选；
android 淘宝端使用桥方法相册只支持单选，因为WindVane 图库多选能力在Android 13及以上存在问题，所以限制为单选了，refer to https://alidocs.dingtalk.com/i/nodes/QOG9lyrgJPPNL2rXIE5KE35LJzN67Mw4?corpId=dingd8e1123006514592&utm_medium=im_card&iframeQuery=utm_medium%3Dim_card%26utm_source%3Dim&dontjump=true&utm_scene=team_space&utm_source=im&cid=52736710746


### openVerify

是否开启校验功能，目前支持校验最大图片选择数量，超出了报错 LIMIT_EXCEEDED

## 图片处理相关参数详情

### skipImageCompress

是否跳过图片压缩环节，如果跳过，压缩相关props: `autoCompress`, 压缩过程的`quality` 将无效; 缺省或 false 不跳过压缩。
图片压缩会降低图片质量，可以节省上传时间。
使用`ORIGIN`上传模式建议压缩后上传。
pdf会自动跳过压缩， skipImageCompress 不为true, 也不会进行压缩。

### autoCompress
是否自动压缩，默认开启，如果不是特殊场景，建议默认开启；
将超过600k的图片压缩到600k以下；不压缩pdf；
如果传入有效的quality, 则不会进行自动压缩，quality 优先级高；


### ~~imgCanvasOptions~~（已弃用）
**imgCanvasOptions 在图片上传过程中可能使用0、1、2次，实际效果无法预测。
经检索，只有一处用到了 scale 0.6, 在压缩时会被应用到，可能被缩放0.6 或0.36， 去掉影响较小。
根据使用情况决定移除本设置。（底层函数：`image2Canvas`，`compress`, `getBase64ByURL`函数处理图片的能力依然保留。）**

在图片转dataUrl（端侧获取到的是本地临时预览地址时需要转dataUrl） 及 图片压缩过程，将一个image对象转变为一个canvas对象用到的参数，也就是说imgCanvasOptions中的参数只有进行了image对象转canvas，才会生效。如果图片没有转dataUrl 并且跳过压缩，本项设置不会生效。
进行压缩的条件是 `(skipImageCompress !== true && (quality || autoCompress))`，即 `skipImageCompress`不为true且有quality 或这 `skipImageCompress`不为true 且 `autoCompress` 为 true。

| Prop        | Type     | Required | Default | Description                                                                                                                  |
| :---------- | :------- | :------- | :------ | :--------------------------------------------------------------------------------------------------------------------------- |
| width       | `number` | `false`  |         | canvas 图像的宽度, 如果只设置了height, 则默认按照比例缩放，如果没有设置height,默认为 image 的宽度；优先级低于width和height设置，如果设置了合理的scale，width,height 设置将无效 |
| height      | `number` | `false`  |         | canvas 图像的高度，如果只设置了width, 则默认按照比例缩放，如果没有设置width,默认为 image 的宽度；优先级低于width和height设置，如果设置了合理的scale，width,height 设置将无效 |
| scale       | `number` | `false`  |         | 相对于 image 的缩放比例，范围 0-10，默认不缩放；优先级高于width和height设置，如果设置了合理的scale，width,height 设置将无效  |
| orientation | `number` | `false`  |         | 参数旋转方向、1-0°、2-水平翻转、3-180°、4-垂直翻转、5-顺时针 90°+水平翻转、6-顺时针 90°、7-顺时针 90°+垂直翻转、8-逆时针 90° |

### quality

作为第二个参数 `encoderOptions` 传入 `canvas.toDataURL('image/jpeg', encoderOptions)`，用以表示图像质量。
不传则采用默认压缩算法;只有在skipImageCompress为false 时生效。不压缩pdf;

quality 是一个介于0和1之间的数字，其中1是最高质量（最少压缩）而0是最低质量（最大压缩）。它会影响输出图像的文件大小和视觉清晰度，即使设置为1，由于JPEG是一种有损压缩格式，也不代表完全无损。同样，quality参数值过低（在接近0的范围内）会明显降低图像质量，导致较大的压缩伪影和图像模糊。高质量会生成更大的文件大小，低质量则相反。
如果未指定quality参数或者使用了undefined，则浏览器会使用默认的图像质量。这个默认值因浏览器而异，可能在品质上各不相同。

### onStatusChange

监听上传状态
| Prop             | Type                                                     | Required | Default |stage| Description                                                                    |
| :--------------- | :---------------------------------------------           | :------- | :------ |:---  | :----------------------------------------------------------------------------- |
| onStatusChange   | `(type: STATUS_TYPE, statusDetail:StatusDetail[]) => void;` | `false`  |         |各阶段| 上传前各阶段钩子函数, 会返回图片本地虚拟 URL，或者 base64 字符串，或者图片链接 |

#### STATUS_TYPE

`选择图片后 CHOOSE`
`压缩完成后 COMPRESS`
`转base64后 URL_CONVERSION`
`upload中 UPLOAD_ING`
`upload结束 UPLOAD_END`

#### StatusDetail

```Typescript
type StatusDetail = {
  /**
   * 图片地址，获取远程地址前，值为本地图片地址，获取远程地址后，值为远程地址
   */
  url: string;
  /**
   * 本地图片地址，用于上传成功前预览图片
   */
  localUrl: string;
  /**
   * 隐私图片objectName, 请求获取（UPLOAD_ING）后才有
   */
  objectName?: string;
  /**
   * 结果
   * 上传完后（UPLOAD_END）才有，标识上传结果
   */
  success?: boolean;  
  /**
   * 错误信息
   * 上传完后（UPLOAD_END）才有，显示失败原因
   */
  error?: string;
};
```

### uploadConfig

| Prop            | Type                                                                  | Required | Default | Description                                                                |
| :-------------- | :-------------------------------------------------------------------- | :------- | :------ | :------------------------------------------------------------------------- |
| mode          | `UPLOAD_MODE`  | `false`  | 'ORIGIN'      | 上传方式选择                                                                   |
| prefix          | `string`    | `false`  | 无    | 文件前缀, 可以找悦加（396390）申请获取；强烈建议开发测试时加入此prefix=temp，避免开发过程中测试的图片留存太久了造成oss空间浪费，此前缀文件资源只留存三天。                                                                   |
| subDomain       | 'm' \| 'wapa' \| 'waptest'                                              | `false`  | 无      | mtop请求二级域，当前环境（日常 'waptest'、预发 'wapa'、生产 'm'；不传，自动匹配环境，传入则固定使用传入环境。预发环境不稳定时，可以通过 subDomain传入m 在预发环境改用线上环境；仅web端有效，飞猪淘宝支付宝切换环境通过 app 内工具切换， eg.Atom   |
| expires         | `number`  | `false` | 无      | 不传，图片将作为公共图片上传，返回fli.alicdn.com 图片地址，不需要加签即可访问；传入有效值，图片作为隐私图片上传，返回 images1.fliggy.com 图片地址，地址有效期限为传入值，单位（秒）；时间超出有效值需要重新加签才能正常访问。 飞猪、淘宝、支付宝做了[cdn域名收敛](https://code.alibaba-inc.com/wireless/tbcdnimage#3%E3%80%81%E5%9F%9F%E5%90%8D%E6%94%B6%E6%95%9B)，会影响公共图片的上传和回显。目前只有`ORIGIN`模式支持公共图片上传。|
| process         | `string`                                                              | `false`  | 无      | 图像处理指令,比如加水印; 暂不支持                                                |
| customBucket    | `string`                                                              | `false`  | 无      | 定制 bucket 上传（普通接入无需传入）必须上传base64文件                                       |
| onBeforeRequest | `(baseUrl: string, isFragmentation?: boolean) => CommonRequestConfig` | `false`  | 无      | 上传接口前置回调，支持改造接口                                             |
| onAfterRequest  | `(result: CommonResponse) => any`                                     | `false`  | 无      | 上传接口后置回调，支持改造接口                                             |

#### UPLOAD_MODE

``` typescript
enum UPLOAD_MODE {
  ORIGIN = 'ORIGIN', // 3.0.0版本前的原有上传模式，faas 转发上传 oss 服务，性能较差，不推荐
  OSS_URL = 'OSS_URL', // oss 直传，put 请求上传，不依赖 oss sdk； 3.0.0 及以上版本支持
  OSS_TOKEN = 'OSS_TOKEN', // oss 直传， 依赖 oss sdk； 3.0.0 及以上版本支持
}
```

目前只有`ORIGIN`模式支持公共图片上传。飞猪、淘宝、支付宝做了[cdn域名收敛](https://code.alibaba-inc.com/wireless/tbcdnimage#3%E3%80%81%E5%9F%9F%E5%90%8D%E6%94%B6%E6%95%9B)，会影响公共图片的上传和回显。


#### prefix

文件前缀, 可以找溪童（112095）申请获取。
建议不同业务申请不同的 prefix 用以区分。
强烈建议开发测试时加入此 prefix="temp"，避免开发过程中测试的图片留存太久了造成oss空间浪费，此前缀文件资源只留存三天。

#### expires

- 公共图片上传：不传expires，返回fli.alicdn.com 图片地址，不需要加签即可访问
- 隐私图片上传：传入expires有效值，图片作为隐私图片上传，返回 images1.fliggy.com 图片地址，地址有效期限为传入值，单位（秒）；时间超出有效值需要重新加签才能正常访问。 

飞猪、淘宝、支付宝做了cdn域名收敛，会影响公共图片的上传和回显。目前只有ORIGIN模式支持公共图片上传。

#### process

图像处理指令,比如加水印
只有`ORIGIN`模式支持，上传图片时作为参数发送到服务端，利用ali oss 能力处理。


#### customBucket

定制 bucket 上传（普通接入无需传入）必须上传base64文件。
只有`ORIGIN`模式支持。
定制bucket上传为历史功能，只修bug，不再更新，也不再接收新的定制bucket需求。


## 单独使用上传能力

如果你希望跳过图片获取环节直接传入图片并上传，可以使用我们导出的系列上传函数：

- upload：3.0之前的旧版本导出的 upload 方法
- uploadByBase64： 重构后的 upload 方法；性能较直传稍差，支持鸿蒙，不支持小程序
- uploadByPutUrl： 新增的 OSS_URL 直传通道上传方法；不支持鸿蒙，不支持小程序
- uploadByToken： 新增的 OSS_TOKEN 直传通道上传方法; 不支持鸿蒙，不支持小程序

具体用法

`upload(files: string[], uploadConfig, onStatusChange?): Promise<{url: string, objectName?: string}[]>;`

`uploadByBase64(files: (File | string)[], uploadConfig, onStatusChange?): Promise<{url: string, objectName?: string}[]>;`

`uploadByPutUrl(files: (File | string)[], uploadConfig, onStatusChange?): Promise<{url: string, objectName?: string}[]>;`

`uploadByToken(files: (File | string)[], uploadConfig, onStatusChange?): Promise<{url: string, objectName?: string}[]>;`

第一个参数为图片数组，元素为图片 base64 字符串 或 File 对象，其中 `upload`  只支持传入字符串。
`uploadConfig` 同上文中的default 函数参数中的 `uploadConfig`，无须 `mode`；
`onStatusChange` 同上文中的default 函数参数中 `onStatusChange`。

代码示例：
```typescript
import { uploadByPutUrl, chooseImg } from '@ali/fliggy-image-upload';


const uploadConfig = {
  prefix: 'default',
}

const onStatusChange = (type, statusDetail) => {
  console.log(type, statusDetail);
}

chooseImg({ maxSelect: 9 }, true, true).then(
  (images) => {
    uploadByPutUrl(images, uploadConfig, onStatusChange);
  },
);
```


## 性能测试数据
详情参考： [图片上传性能测试](https://aliyuque.antfin.com/fliggy-fed-feedback/privacy_pictures/btu19amu3o9ivp8g?singleDoc#)

新通道：mode=OSS_URL 或 OSS_TOKEN；原通道：mode=ORIGIN。
- 上传1kB超小图片新通道耗时略有减少，不同配置上传耗时均值均在1秒左右，无明显差异；
- 上传83kB 小图，新通道较原通耗时减少 60%左右
- 上传1.5MB 图片，新通道较原通道性耗时减少70%左右。
- 上传6.5M 图片：
  - 新通道耗时较原通道至少减少 76%
  - 公共图片上传较隐私图片耗时少至少 20%
  - 压缩较不压缩耗时少 50% 左右


## 隐私图片上传服务用户文档
[简介](https://aliyuque.antfin.com/fliggy-fed-feedback/privacy_pictures/pg9qdb2ylkfozkp0?singleDoc#)
[接入说明](https://aliyuque.antfin.com/fliggy-fed-feedback/privacy_pictures/qq28e4m1pi3lm8vg?singleDoc#)


## 开发

1. `tnpm install` 安装依赖 node modules
2. `clam newbranch` 开新分支
3. `clam dev` 启动本地服务，自动打开默认浏览器开发调试
4. `clam build` 执行构建
5. `clam prepub` 执行构建并推送远程 `daily/x.y.z` 分支;
6. `clam publish` 执行构建推送远程 `daily/x.y.z` 分支并推送 `publish/x.y.z` tag，同时发布到 tnpm `@ali` 私有域下

## 更新日志

见 [CHANGELOG.md](http://gitlab.alibaba-inc.com/rxpi/fliggy-image-upload/blob/master/CHANGELOG.md)。

    /**
     * 仅web支持；
     * 二级域，当前环境（日常 'waptest'、预发 'wapa'、生产 'm'），默认'm'
     */
    subDomain?: 'm' | 'wapa' | 'waptest';
