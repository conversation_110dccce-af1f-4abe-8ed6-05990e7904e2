{"compilerOptions": {"module": "esNext", "target": "es2015", "jsx": "preserve", "jsxFactory": "createElement", "moduleResolution": "node", "alwaysStrict": true, "sourceMap": false, "allowSyntheticDefaultImports": true, "suppressImplicitAnyIndexErrors": true, "removeComments": false, "preserveConstEnums": true, "experimentalDecorators": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": false, "noImplicitAny": false, "noImplicitThis": false, "resolveJsonModule": true, "allowJs": false, "outDir": "build/lib", "rootDir": "src", "baseUrl": "."}, "include": ["src/**/*", "src/pages/demo/components/config/ImageCanvasOptions.tsx"], "exclude": ["node_modules", "build/lib"]}