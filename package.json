{"name": "@ali/rxpi-fliggy-image-upload", "widgetName": "fliggy-image-upload", "version": "3.0.9", "description": "飞猪前端上传图片组件", "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["es", "lib", "src"], "universal": true, "clam2Version": "1.8.10", "lib": "rax", "type": "weex", "rxpiType": "api", "group": "trip", "repository": {"type": "git", "url": "**************************:trip/fliggy-image-upload.git"}, "miniprogram": ".", "keywords": ["fliggy-image-upload"], "author": {"name": "江渺", "email": "<EMAIL>"}, "license": "BSD", "devDependencies": {"@ali/eslint-config-fliggy": "^1.0.7", "@types/ali-app": "^1.0.1", "@types/ali-oss": "^6.16.11", "@types/rax": "^1.0.0", "@typescript-eslint/eslint-plugin": "^5.50.0", "@typescript-eslint/parser": "^5.50.0", "babel-eslint": "^8.2.6", "driver-universal": "^3.5.0", "eslint": "^6.8.0", "eslint-config-ali": "^14.0.2", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-plus": "^0.1.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "jsx2mp-runtime": "^0.4.0", "rax": "^1.0.0", "rax-image": "^2.0.0", "rax-recyclerview": "^1.0.0", "rax-text": "^2.0.0", "rax-view": "^2.0.0", "typescript": "^4.9.5"}, "dependencies": {"@ali/alitrip-bridge": "^3.0.22", "@ali/alitrip-env": "^1.1.11", "@ali/rxpi-env": "^2.4.2", "@ali/rxpi-mtop": "1.14.0", "@ali/rxpi-tab-header": "^3.1.2", "@ali/rxpi-user": "^1.6.0", "@ali/rxpi-utils": "^2.15.23", "@ali/uniapi-ability": "^2.0.20", "@ali/uniapi-photo": "^1.1.29", "@aliyun-sls/web-track-browser": "^0.0.3", "@uni/env": "^1.0.7", "@uni/image": "^1.1.3", "base64-js": "^1.5.1", "md5": "^2.3.0", "prop-types": "^15.6.0", "require-esm": "^1.0.0", "universal-env": "^3.3.3"}, "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "toolkit": "@ali/clam-toolkit-rxpi", "defPublishType": "pegasus", "commandType": "clam", "scripts": {"check-types": "tsc --project tsconfig.check.json"}}